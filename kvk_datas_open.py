
# This file was generated by the <PERSON>kinter Designer by <PERSON><PERSON> Jadhav
# https://github.com/ParthJadhav/Tkinter-Designer
import tkinter as tk
from pathlib import Path

# from tkinter import *
# Explicit imports to satisfy Flake8
from tkinter import Tk, Canvas, Entry, Text, Button, PhotoImage

class kvk_datas:
    def __init__(self, window, expire):
        OUTPUT_PATH = Path(__file__).parent
        ASSETS_PATH = OUTPUT_PATH / Path(r"assets\frame0")


        def relative_to_assets(path: str) -> Path:
            return ASSETS_PATH / Path(path)


        window = tk.Toplevel()
        self.window = window
        self.window.geometry("550x350")
        self.window.configure(bg = "#FFFFFF")


        self.canvas = Canvas(
            self.window,
            bg = "#FFFFFF",
            height = 350,
            width = 550,
            bd = 0,
            highlightthickness = 0,
            relief = "ridge"
        )

        self.canvas.place(x = 0, y = 0)
        self.image_image_1 = PhotoImage(
            file=relative_to_assets("image_1.png"))
        self.image_1 = self.canvas.create_image(
            275.0000305175781,
            177.18245315551758,
            image=self.image_image_1
        )

        self.entry_image_1 = PhotoImage(file=relative_to_assets("entry_1.png"))
        self.entry_bg_1 = self.canvas.create_image(138.5,168.0,image=self.entry_image_1)
        self.entry_1 = Entry(bd=0,bg="#999999",fg="#000716",highlightthickness=0)
        self.entry_1.place(x=133.0,y=158.0,width=11.0,height=18.0)

        self.entry_image_2 = PhotoImage(
            file=relative_to_assets("entry_2.png"))
        self.entry_bg_2 = self.canvas.create_image(
            138.5,
            137.0,
            image=self.entry_image_2
        )
        self.entry_2 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_2.place(
            x=133.0,
            y=127.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_3 = PhotoImage(
            file=relative_to_assets("entry_3.png"))
        self.entry_bg_3 = self.canvas.create_image(
            43.5,
            75.0,
            image=self.entry_image_3
        )
        self.entry_3 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_3.place(
            x=38.0,
            y=65.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_4 = PhotoImage(
            file=relative_to_assets("entry_4.png"))
        self.entry_bg_4 = self.canvas.create_image(
            138.5,
            106.0,
            image=self.entry_image_4
        )
        self.entry_4 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_4.place(
            x=133.0,
            y=96.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_5 = PhotoImage(
            file=relative_to_assets("entry_5.png"))
        self.entry_bg_5 = self.canvas.create_image(
            138.5,
            75.0,
            image=self.entry_image_5
        )
        self.entry_5 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_5.place(
            x=133.0,
            y=65.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_6 = PhotoImage(
            file=relative_to_assets("entry_6.png"))
        self.entry_bg_6 = self.canvas.create_image(
            43.5,
            106.0,
            image=self.entry_image_6
        )
        self.entry_6 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_6.place(
            x=38.0,
            y=96.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_7 = PhotoImage(
            file=relative_to_assets("entry_7.png"))
        self.entry_bg_7 = self.canvas.create_image(
            43.5,
            137.0,
            image=self.entry_image_7
        )
        self.entry_7 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_7.place(
            x=38.0,
            y=127.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_8 = PhotoImage(
            file=relative_to_assets("entry_8.png"))
        self.entry_bg_8 = self.canvas.create_image(
            43.5,
            168.0,
            image=self.entry_image_8
        )
        self.entry_8 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_8.place(
            x=38.0,
            y=158.0,
            width=11.0,
            height=18.0
        )

        self.entry_image_9 = PhotoImage(
            file=relative_to_assets("entry_9.png"))
        self.entry_bg_9 = self.canvas.create_image(
            43.5,
            199.0,
            image=self.entry_image_9
        )
        self.entry_9 = Entry(
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_9.place(
            x=38.0,
            y=189.0,
            width=11.0,
            height=18.0
        )

        self.canvas.create_text(
            65.0,
            69.0,
            anchor="nw",
            text="T1 Points",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            160.0,
            69.0,
            anchor="nw",
            text="deaths",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            160.0,
            162.0,
            anchor="nw",
            text="ally helps",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            160.0,
            131.0,
            anchor="nw",
            text="honor points",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            160.0,
            99.0,
            anchor="nw",
            text="rss assist",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            65.0,
            100.0,
            anchor="nw",
            text="T2 Points",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            65.0,
            131.0,
            anchor="nw",
            text="T3 Points",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            65.0,
            162.0,
            anchor="nw",
            text="T4 Points",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            65.0,
            193.0,
            anchor="nw",
            text="T5 Points",
            fill="#FFFFFF",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            32.0,
            29.0,
            anchor="nw",
            text="INSERT THE POINTS FOR EACH STAT",
            fill="#E0E0E0",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_rectangle(
            120.0,
            52.0,
            121.0,
            199.0,
            fill="#8D8C8C",
            outline="")

        self.canvas.create_rectangle(
            17.0,
            42.0,
            224.99990844726562,
            44.014705657958984,
            fill="#837F7F",
            outline="")

        self.canvas.create_rectangle(
            292.0,
            83.0,
            499.9999237060547,
            85.01470565795898,
            fill="#837F7F",
            outline="")

        self.canvas.create_rectangle(
            16.0,
            42.0,
            18.0,
            254.0,
            fill="#847F7F",
            outline="")

        self.canvas.create_rectangle(
            17.0,
            253.0,
            225.0,
            254.0,
            fill="#847F7F",
            outline="")

        self.canvas.create_rectangle(
            292.0,
            246.0,
            500.0,
            247.0,
            fill="#847F7F",
            outline="")

        self.canvas.create_rectangle(
            224.0,
            42.0,
            225.0,
            254.0,
            fill="#847F7F",
            outline="")

        self.canvas.create_rectangle(
            292.0,
            83.0,
            293.0,
            247.0,
            fill="#847F7F",
            outline="")

        self.canvas.create_rectangle(
            499.0,
            83.0,
            500.0,
            247.0,
            fill="#847F7F",
            outline="")

        self.button_image_1 = PhotoImage(
            file=relative_to_assets("button_1.png"))
        self.button_1 = Button(
            image=self.button_image_1,
            borderwidth=0,
            highlightthickness=0,
            command=lambda: print("button_1 clicked"),
            relief="flat"
        )
        self.button_1.place(
            x=240.0,
            y=280.0,
            width=70.0,
            height=27.0
        )

        self.button_image_2 = PhotoImage(
            file=relative_to_assets("button_2.png"))
        self.button_2 = Button(
            image=self.button_image_2,
            borderwidth=0,
            highlightthickness=0,
            command=lambda: print("button_2 clicked"),
            relief="flat"
        )
        self.button_2.place(
            x=343.9655456542969,
            y=48.0,
            width=105.03443908691406,
            height=16.224042892456055
        )

        self.canvas.create_text(
            336.0,
            33.0,
            anchor="nw",
            text="UPLOAD YOUR DATA FILE",
            fill="#E0E0E0",
            font=("Inter", 10 * -1)
        )

        self.canvas.create_text(
            336.0,
            73.0,
            anchor="nw",
            text="Choose which sheets you want to load",
            fill="#E0E0E0",
            font=("Inter", 7 * -1)
        )
        self.window.resizable(False, False)

