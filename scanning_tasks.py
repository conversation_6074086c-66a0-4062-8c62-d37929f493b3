import numpy
from PIL import Image, ImageEnhance
import pytesseract
import subprocess
import io
import PIL.Image
from time import sleep
import cv2
import numpy as np
from tkinter import messagebox


def modify_image(re_open):
    img = cv2.resize(re_open, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
    # Convert the image to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Apply a threshold to the image to make the text more visible
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]
    # Perform some morphological operations to remove noise and smooth the image
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
    dilate = cv2.dilate(opening, kernel, iterations=1)
    return dilate
def tap(x,y, n, IP,PORT):
    adb_path = 'adb\\adb.exe'
    for i in range(n):
        subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}', stderr=subprocess.PIPE)
    return sleep(0.5)

def Screenshot(IP,PORT):
    adb_path = 'adb\\adb.exe'
    screenshot = subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} exec-out screencap -p',stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    #screenshot = subprocess.run(f'adb -P 5038 -s {str(IP).strip()}:{PORT} exec-out screencap -p', stdout=subprocess.PIPE,
    #                            stderr=subprocess.PIPE)
    #print(PIL.Image.open(io.BytesIO(screenshot.stdout)))
    return PIL.Image.open(io.BytesIO(screenshot.stdout))


def ranking_open(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    print('Checking ranking list:')
    while ready == None:
        screenshot = Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/ranking_one.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        if max_val < 0.7:
            sleep(0.5)
            ready = None
        else:
            ready = True
            print('Checking ranking list: True')
        sleep(1)
    sleep(1)

def profile_opened(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    while ready == None:
        pass


def check_power_values(df):
    corrected_powers = []
    prev_row_power = None  # Initialize prev_row_power with None for the first iteration
    for i in range(len(df)):
        row_power = df.iloc[i]['power']
        next_row_power = df.iloc[i + 1]['power'] if i < len(df) - 2 else None
        # Check if row_power should be corrected
        if (next_row_power and row_power < next_row_power) or (prev_row_power and prev_row_power < row_power):
            if str(row_power).startswith('1'):
                # Correct row_power by replacing the first '1' with '7'
                corrected_power = int('7' + str(row_power)[1:])
                corrected_powers.append(corrected_power)
            elif str(row_power).startswith('7') and prev_row_power and len(str(row_power)) > len(str(prev_row_power)):
                # Correct row_power by replacing the first two characters with '7'
                corrected_power = int('7' + str(row_power)[2:])
                corrected_powers.append(corrected_power)
            else:
                corrected_powers.append(row_power)  # No correction needed
        else:
            corrected_powers.append(row_power)  # No correction needed
        prev_row_power = row_power  # Update prev_row_power for the next iteration
    return corrected_powers

def inizialize(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    while ready == None:
        screenshot = Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/in_city.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        target1 = cv2.imread('images/out_of_city.png', 0)
        h, w = target.shape
        result1 = cv2.matchTemplate(screenshot, target1, cv2.TM_CCOEFF_NORMED)
        min_val1, max_val1, min_loc1, max_loc1 = cv2.minMaxLoc(result1)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location1 = min_loc1
        else:
            location1 = max_loc1

        if max_val < 0.7 and max_val1 < 0.7:
            sleep(1)
            subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent 4', stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE)
            ready = None
        else:
            if max_val1 > 0.7:
                tap(location1[0],location1[1],1,IP,PORT)
            ready = True
        sleep(1)
    sleep(1)

def loading_new_kd(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    while ready == None:
        screenshot = Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/in_city.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        if max_val < 0.9:
            sleep(2)
        else:
            ready = True

def start_scanning(IP,PORT):
    tap(50, 38, 1, IP,PORT) #profile tap
    tap(397, 597, 1, IP,PORT) # rankings tap
    tap(350, 420, 1, IP,PORT) #individual power ranking tap


def scan_id(profile_image, IP, PORT):
    id_box = (585, 155, 680, 180)
    new_screenshot = Screenshot(IP, PORT)
    new_id_image = new_screenshot.crop(id_box)
    print('first attempt empty')
    cv2.imwrite(f'temp_id.png', np.uint8(new_id_image))
    re_open = Image.open(f'temp_id.png').convert('L')
    enhancer = ImageEnhance.Contrast(re_open)
    re_open = enhancer.enhance(15.0)
    id = pytesseract.image_to_string(re_open, config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
    if id == '':
        print('still empty')
    return id

def scan_name(profile_image):
    name_box = (512, 210, 720, 250)
    name_image = profile_image.crop(name_box)
    img_gray = cv2.cvtColor(numpy.uint8(name_image), cv2.COLOR_BGR2GRAY)
    name = pytesseract.image_to_string(img_gray).replace('\n', '')
    return name

def scan_power(profile_image, IP,PORT):
    power_box = (698, 259, 851, 293)
    counter = 0
    power= ''
    while power == '' and counter <= 5:
        new_power_image = profile_image.crop(power_box)
        cv2.imwrite(f'temp_power.png', numpy.uint8(new_power_image))
        re_open = cv2.imread('temp_power.png')
        dilate = modify_image(re_open)
        power = pytesseract.image_to_string(dilate,
                                            config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter +=1
    return int(power)

def scan_alliance(profile_image):
    alliance_box = (473, 262, 676, 290)
    alliance_image = profile_image.crop(alliance_box)
    img_gray_ally = cv2.cvtColor(numpy.uint8(alliance_image), cv2.COLOR_BGR2GRAY)
    alliance = pytesseract.image_to_string(img_gray_ally).replace('\n', '')
    return alliance

def scan_kill_points(kill_points_info, IP, PORT):
    kill_points_box = (891, 265, 1063, 291)
    #kill_points_image = kill_points_info.crop(kill_points_box)
    #img_gray_kp = cv2.cvtColor(numpy.uint8(kill_points_image), cv2.COLOR_BGR2GRAY)
    #kill_points = pytesseract.image_to_string(img_gray_kp, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
    counter = 0
    kill_points = ''
    while kill_points == '' and counter <= 5:
        screenshot = kill_points_info
        new_power_image = screenshot.crop(kill_points_box)
        cv2.imwrite(f'temp_kp.png', numpy.uint8(new_power_image))
        re_open = cv2.imread('temp_kp.png')
        dilate = modify_image(re_open)
        kill_points = pytesseract.image_to_string(dilate,
                                            config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter +=1
    try:
        kill_points = int(kill_points)
    except:
        kill_points = kill_points
    return kill_points

def scan_tier_kills(tier_screenshot, IP,PORT):
    #tier_kills_box = (678, 477, 913, 640)
    t1_box = (689,338,794,363)
    t2_box = (689,373,794,397)
    t3_box = (689,411,794,432)
    t4_box = (689,444,794,468)
    t5_box = (689, 479, 794, 510)
    ranged_points_box = (973,567,1135,592)
    ranged_points = ''
    counter = 0
    while ranged_points == '' and counter <= 5:
        sleep(1)
        sc = tier_screenshot
        ranged_points_image = sc.crop(ranged_points_box)
        cv2.imwrite(f'temp.png', np.uint8(ranged_points_image))
        #re_open = Image.open(f'temp.png').convert('L')
        re_open = cv2.imread('temp.png')
        #dilate = modify_image(re_open)
        # --psm 13
        ranged_points = pytesseract.image_to_string(re_open, config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')

        counter += 1

    t1 = ''
    counter = 0
    while t1 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t1_image = sc.crop(t1_box)
        cv2.imwrite(f'temp.png', np.uint8(t1_image))
        re_open = cv2.imread('temp.png')
        #dilate = modify_image(re_open)
        # --psm 13
        t1 = pytesseract.image_to_string(re_open,
                                                    config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1
    t2 = ''
    counter = 0
    while t2 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t2_image = sc.crop(t2_box)
        cv2.imwrite(f'temp.png', np.uint8(t2_image))
        re_open = cv2.imread('temp.png')
        #dilate = modify_image(re_open)
        # --psm 13
        t2 = pytesseract.image_to_string(re_open,
                                                    config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    t3 = ''
    counter = 0
    while t3 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t3_image = sc.crop(t3_box)
        cv2.imwrite(f'tempt3.png', np.uint8(t3_image))
        re_open = cv2.imread('tempt3.png')
        #dilate = modify_image(re_open)
        # --psm 13
        t3 = pytesseract.image_to_string(re_open,
                                                    config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1
        print(t3)

    t4 = ''
    counter = 0
    while t4 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t4_image = sc.crop(t4_box)
        cv2.imwrite(f'temp.png', np.uint8(t4_image))
        re_open = cv2.imread('temp.png')
        #dilate = modify_image(re_open)
        # --psm 13
        t4 = pytesseract.image_to_string(re_open,
                                                    config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    t5 = ''
    counter = 0
    while t5 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t5_image = sc.crop(t5_box)
        cv2.imwrite(f'temp.png', np.uint8(t5_image))
        re_open = cv2.imread('temp.png')
        #dilate = modify_image(re_open)
        # --psm 13
        t5 = pytesseract.image_to_string(re_open,
                                                    config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1


    tier_kills_prov = [t1,t2,t3,t4,t5, ranged_points]
    tier_kills = []
    for i in tier_kills_prov:
        try:
            tier_kills.append(int(i))
        except:
            if i == '':
                tier_kills.append(0)
            else:
                tier_kills.append(i)
    return tier_kills

def scan_deaths(more_info_image):
    deaths_box = (900, 357, 1065, 389)
    deaths = ''
    counter = 0
    while deaths == '' and counter <= 5:
        deaths_image = more_info_image.crop(deaths_box)
        img_gray_deaths = cv2.cvtColor(numpy.uint8(deaths_image), cv2.COLOR_BGR2GRAY)
        cv2.imwrite(f'temp_deaths.png', np.uint8(img_gray_deaths))
        re_open = cv2.imread('temp_deaths.png')
        dilate = modify_image(re_open)
        #--psm 13
        deaths = pytesseract.image_to_string(dilate, config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1
    try:
        deaths = int(deaths)
    except:
        deaths = deaths
    return deaths

def scan_ally_helps(more_info_image):
    ally_helps_box = (950, 590, 1050, 620)
    ally_helps = ''
    counter = 0
    while ally_helps == '' and counter <= 5:
        ally_helps_image = more_info_image.crop(ally_helps_box)
        #img_gray_ally_helps = cv2.cvtColor(numpy.uint8(ally_helps_image), cv2.COLOR_BGR2GRAY)
        cv2.imwrite(f'temp_helps.png', np.uint8(ally_helps_image))
        re_open = cv2.imread('temp_helps.png')
        dilate = modify_image(re_open)
        ally_helps = pytesseract.image_to_string(dilate, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1
    try:
        ally_helps = int(ally_helps)
    except:
        ally_helps = ally_helps

    return ally_helps



def scan_rss_assistance(more_info_image):
    rss_assistance_box = (848, 537, 1052, 571)
    rss_assistance_image = more_info_image.crop(rss_assistance_box)
    img_gray_rss = cv2.cvtColor(numpy.uint8(rss_assistance_image), cv2.COLOR_BGR2GRAY)
    rss_assistance = pytesseract.image_to_string(img_gray_rss, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
    try:
        rss_assistance = int(rss_assistance)
    except:
        rss_assistance = rss_assistance
    return rss_assistance

def scan_real_name(IP,PORT):
    adb_path = 'adb\\adb.exe'
    tap(298, 124, 1, IP,PORT)  # tapping on copy_name

    log = subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell logcat -d', stdout=subprocess.PIPE,
                         stderr=subprocess.PIPE, encoding='utf-8')
    sub1 = 'hcallSetClipboardTextRpc('
    pos1 = str(log.stdout).find(sub1)
    partially_cleaned = str(log)[pos1:]
    sub2 = ')'
    pos2 = partially_cleaned.find(sub2)
    cleaned_name = partially_cleaned[166:pos2]
    sub3 = 'hcallSetClipboardTextRpc('
    pos3 = cleaned_name.find(sub3)
    real_name = cleaned_name[pos3 + 25:]
    return real_name

def clear_logs(IP,PORT):
    adb_path = 'adb\\adb.exe'
    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell logcat -b all -c', stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

def check_profile(IP, PORT):
    pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"
    adb_path = 'adb\\adb.exe'
    tap(65,655,1,IP,PORT)
    sleep(1)
    tap(304, 22, 1, IP, PORT)
    sleep(1)
    tap(435, 142, 1, IP, PORT)
    sleep(1)
    check_kd_screenshot = Screenshot(IP,PORT)
    current_kd_box = (6, 641, 98, 696)
    current_kd_image = check_kd_screenshot.crop(current_kd_box)
    img_gray = cv2.cvtColor(numpy.uint8(current_kd_image), cv2.COLOR_BGR2GRAY)
    current_kd = pytesseract.image_to_string(img_gray,config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
    tap(348,423,1, IP,PORT)
    sleep(1)
    tap(348, 423, 1,IP,PORT)
    return current_kd

def extract_n_scans(IP,PORT):
    pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"
    print('Process to detect the number of total scans: Started')
    #press profile pic
    tap(50,45,1,IP,PORT)
    sleep(1)
    #press settings
    tap(1008, 584, 1, IP, PORT)
    sleep(1)
    # press characters
    tap(346, 376, 1, IP, PORT)
    sleep(5)
    #read the number
    box = (1018,159,1062,189)
    screenshot = Screenshot(IP,PORT).crop(box)

    img_gray = cv2.cvtColor(numpy.uint8(screenshot), cv2.COLOR_BGR2GRAY)
    n_scans = pytesseract.image_to_string(img_gray,
                                                 config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
    print(f'Number of scans detected: {n_scans}')

    #make sure the arrow is pointing up
    print('Checking if arrow is pointing up')
    screenshot_profiles = Screenshot(IP, PORT)
    screenshot_profiles = cv2.cvtColor(np.array(screenshot_profiles), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/star.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(screenshot_profiles, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val < 0.9:
        print(max_val)
        sleep(2)
        tap(1080,170,1,IP,PORT)
        sleep(1)
    return n_scans


def check_flag(IP, PORT):

    screenshot_profiles = Screenshot(IP, PORT)
    screenshot_profiles = cv2.cvtColor(np.array(screenshot_profiles), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/green_flag.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(screenshot_profiles, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.9:
        return location  # Return the coordinates of the found image

    # Swipe down until the image is found
    while max_val <= 0.9:
        swipe_down(IP,PORT)  # Implement the swipe_down function according to your requirements
        # Take a new screenshot and perform template matching
        screenshot_profiles = Screenshot(IP, PORT)
        screenshot_profiles = cv2.cvtColor(np.array(screenshot_profiles), cv2.COLOR_RGB2GRAY)
        result = cv2.matchTemplate(screenshot_profiles, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc

    return location

def swipe_down(IP,PORT):
    adb_path = 'adb\\adb.exe'
    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 641 555 641 181 4000', stdout=subprocess.PIPE,
                         stderr=subprocess.PIPE, encoding='utf-8')
    sleep(2)









