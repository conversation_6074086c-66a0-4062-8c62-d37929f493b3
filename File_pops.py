import tkinter as tk
from tkinter import *
from tkinter import ttk
import json
from datetime import datetime
from keyauth import api
import sys

import os
import hashlib
#from GUI_ROKSCAN import ROKSC<PERSON>
from PIL import ImageTk
import PIL.Image
import tkinter.messagebox
import webbrowser

class OpenFile:
    def __init__(self,excel_name):
        window = tk.Toplevel()
        self.window = window
        self.window.geometry('555x260')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('Open')
        self.bg_frame = PIL.Image.open('images\\contact_background.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        def open_excel(e):
            os.system(f"start EXCEL.EXE {excel_name}")
            #webbrowser.open_new('https://discord.gg/ZuxV4eRBTZ')

        def open_folder(e):
            path = os.getcwd() + '\\datas'
            os.startfile(path)




        self.lgn_frame = Frame(self.window, bg='#151E2F', width=350, height=150)
        self.lgn_frame.place(x=110, y=50)

        self.excel = PIL.Image.open('images\\excel_icon.png')
        excel_photo = ImageTk.PhotoImage(self.excel.resize((100,100)))
        self.excel_button_label = Label(self.lgn_frame, image=excel_photo,bg='#151E2F')
        self.excel_button_label.image = excel_photo
        self.excel_button_label.place(x=40, y=15)

        self.excel_button_label.bind("<Button-1>",open_excel)

        self.excel_button = Button(self.lgn_frame, text='Open Excel', font=("yu gothic ui", 10, "bold"), width=12, bd=0,
                            bg='#151E2F', cursor='hand2', activebackground='#151E2F', fg='white', command= lambda : open_excel(1))
        self.excel_button.place(x=40, y=100)

        self.folder = PIL.Image.open('images\\folder_icon.png')
        folder_photo = ImageTk.PhotoImage(self.folder.resize((80,80)))
        self.folder_button_label = Label(self.lgn_frame, image=folder_photo,bg='#151E2F')
        self.folder_button_label.image = folder_photo
        self.folder_button_label.place(x=220, y=15)
        self.folder_button_label.bind("<Button-1>", open_folder)

        self.folder_button = Button(self.lgn_frame, text='Open Folder', font=("yu gothic ui", 10, "bold"), width=12, bd=0,
                                  bg='#151E2F', cursor='hand2', activebackground='#151E2F', fg='white', command = lambda : open_folder(2))
        self.folder_button.place(x=210, y=100)

