import tkinter as tk
from tkinter import *
from tkinter import ttk
import json
from datetime import datetime
from keyauth import api
import sys
#import platform
import os
import hashlib
from GUI_ROKSCAN import ROKSCAN#, KeyPage, RegisterPage, ContactPage
from PIL import ImageTk
import PIL.Image
import tkinter.messagebox
from time import sleep
from Contact import ContactPage


class KeyPage:
    def __init__(self,window):
        def check_key():
            key = self.key_entry.get()
            username = self.username_entry.get()


            def getchecksum():
                md5_hash = hashlib.md5()
                file = open(''.join(sys.argv), "rb")
                md5_hash.update(file.read())
                digest = md5_hash.hexdigest()
                return digest

            keyauthapp = api(
                name="ROKScan",
                ownerid="Ykbj23aFHl",
                secret="e0485dc3f4740a3f9d9de4c95feee7953db5da95f3c10d83c490b8fe56666900",
                version="1.0",
                hash_to_check=getchecksum()
            )
            msg = keyauthapp.upgrade(username, key)
            if msg == 'success':
                global expire
                try:
                    expire = datetime.utcfromtimestamp(int(keyauthapp.user_data.expires))
                except:
                    expire = 'lifetime license'
                self.expired_label = Label(self.lgn_frame, text="successfully upgraded user",
                                           bg="#040405", fg="green",
                                           font=("yu gothic ui", 15, "bold"))
                self.expired_label.place(x=30, y=340)
                self.expired_label.after(2000, lambda: self.expired_label.destroy())
                sleep(3)
                ROKSCAN(window)
            else:
                self.used_label = Label(self.lgn_frame, text=f"{msg}",
                                        bg="#040405",
                                        fg="#dc143c",
                                        font=("yu gothic ui", 12, "bold"))
                self.used_label.place(x=180, y=340)
                self.used_label.after(3000, lambda: self.used_label.destroy())

        def contact_event():
            ContactPage(window)

        self.window = window
        self.window.geometry('800x700')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('Upgrade')
        self.bg_frame = PIL.Image.open('images\\background1.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        self.lgn_frame = Frame(self.window, bg='#040405', width=500, height=500)
        self.lgn_frame.place(x=150, y=70)

        ##CONTACT
        self.cnt_button = PIL.Image.open('images\\contact.png')
        cnt_photo = ImageTk.PhotoImage(self.cnt_button.resize((230,60)))
        self.cnt_button_label = Label(self.lgn_frame, image=cnt_photo, bg='#040405')
        self.cnt_button_label.image = cnt_photo
        self.cnt_button_label.place(x=115, y=380)

        self.contact = Button(self.cnt_button_label, text='CONTACT', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#7f7f7f', cursor='hand2', activebackground='#7f7f7f', fg='#bdb9b1', command= contact_event)
        self.contact.place(x=55, y=12)

        self.txt = "ROKSCAN"
        self.heading = Label(self.lgn_frame, text=self.txt, font=('yu gothic ui', 25, "bold"), bg="#040405",
                             fg='white',
                             bd=5,
                             relief=FLAT)
        self.heading.place(x=100, y=10, width=300, height=30)

        self.sign_in_image = PIL.Image.open('images\\input-key.png')
        photo = ImageTk.PhotoImage(self.sign_in_image.resize((36,30)))

        self.sign_in_image_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.sign_in_image_label.image = photo
        self.sign_in_image_label.place(x=50, y=150)

        self.sign_in_label = Label(self.lgn_frame, text="Insert your license key", bg="#040405", fg="white",
                                    font=("yu gothic ui", 18, "bold"))
        self.sign_in_label.place(x=120, y=110)

        self.key_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui ", 12, "bold"))
        self.key_entry.place(x=110, y=160, width=270)

        self.key_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.key_line.place(x=100, y=190)


        ##UPGRADE
        self.start_button = PIL.Image.open('images\\btn1.png')
        start_photo = ImageTk.PhotoImage(self.start_button.resize((230,60)))
        self.start_button_label = Label(self.lgn_frame, image=start_photo, bg='#040405')
        self.start_button_label.image = start_photo
        self.start_button_label.place(x=115, y=280)
        self.start = Button(self.start_button_label, text='UPGRADE', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#c3c3c3', cursor='hand2', activebackground='#c3c3c3', fg='black', command =check_key)
        self.start.place(x=55, y=12)

        self.username_label = Label(self.lgn_frame, text="Username", bg="#040405", fg="#4f4e4d",
                                    font=("yu gothic ui", 12, "bold"))
        self.username_label.place(x=100, y=225)

        self.username_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui", 12, "bold"))
        self.username_entry.place(x=100, y=245, width=244)

        self.username_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.username_line.place(x=100, y=270)

        self.sign_in_image = PIL.Image.open('images\\hyy.png')
        photo = ImageTk.PhotoImage(self.sign_in_image.resize((36, 30)))

        self.sign_in_image_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.sign_in_image_label.image = photo
        self.sign_in_image_label.place(x=50, y=240)

        self.expired_label = Label(self.lgn_frame, text="Your license key expired, please buy a new one", bg="#040405", fg="white",
                                    font=("yu gothic ui", 15, "bold"))
        self.expired_label.place(x=30, y=340)
        self.expired_label.after(3000, lambda : self.expired_label.destroy())