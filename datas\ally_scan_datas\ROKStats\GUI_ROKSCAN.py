import tkinter

import ally_scan_page
import tkinter as tk
from tkinter import *
from tkinter import ttk, messagebox
import json
from datetime import datetime
from pathlib import Path
import kvk_datas_open
import kvk_datas_page
from keyauth import api
import sys
#import platform
import os
import hashlib
import webbrowser
from Contact import ContactPage
from PIL import ImageTk
import PIL.Image
import threading
import all_info
from SeedScanPage import SeedScan
import subprocess
import requests


__author__ = 'ROKStats'
__copyright__ = ''
__license__ = ''
__version__ = '1.24'
__maintainer__ = 'ROKStats'
__email__ = ''
__status__ = 'Beta'
_AppName_ = 'ROKStats'

class ROKSCAN:
    def __init__(self, window,expire):

        self.window = window
        self.window.geometry('800x700')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('ROKStats - App')

        running= False


        self.bg_frame = PIL.Image.open('images\\background1.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        self.lgn_frame = Frame(self.window, bg='#040405', width=650, height=600)
        self.lgn_frame.place(x=80, y=60)



        self.txt = "ROKSTATS"
        self.heading = Label(self.lgn_frame, text=self.txt, font=('yu gothic ui', 25, "bold"), bg="#040405",
                             fg='white',
                             bd=5,
                             relief=FLAT)
        self.heading.place(x=180, y=10, width=300, height=30)


        ##Menu lines
        self.menu_line = Canvas(self.lgn_frame, width=606, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=75)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=150, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=300, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=452, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=604, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=750, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=50)

        #self.menu_line = Canvas(self.lgn_frame, width=2, height=700, bg="#bdb9b1", highlightthickness=0)
        #self.menu_line.place(x=648, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=650, height=2, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=598)


        def start():
            if not running:
                self.start['state'] = DISABLED
                thread = threading.Thread(target=start_event,daemon=True).start()

        def start_event():
            d = json.load(open('config.json'))
            d['ip'] = self.ip_entry.get()
            d['port'] = self.port_entry.get()
            with open('config.json', 'w') as outfile:
                outfile.write(json.dumps(d, indent=(3)))
            global running
            running = True
            while running:

                IP = self.ip_entry.get()
                PORT = self.port_entry.get()
                KD = self.kd_entry.get()
                gov_num = self.gv_entry.get()
                T1_var = self.T1.get()
                T2_var = self.T2.get()
                T3_var = self.T3.get()
                T4_var = self.T4.get()
                T5_var = self.T5.get()
                rss_ass_var = self.rss_assistance.get()
                dead_var = self.dead.get()
                ally_helps = self.ally_helps.get()
                ranged_points = self.ranged_points.get()

                all_info.full_scan(IP,PORT,KD,gov_num,T1_var,T2_var,T3_var,T4_var,T5_var,rss_ass_var,dead_var, ally_helps, ranged_points)
                new_scan()

        def new_scan():
            self.start['state'] = NORMAL
            global running
            running=False


        def contact_event():
            ContactPage(window)

        def seed_scan_event():
            SeedScan(window,expire)

        def stop_event():
            all_info.Stop()

        def ally_scan_event():
            ally_scan_page.AllyScan(window,expire)

        def open_tutorial(e):
            window = tk.Toplevel()
            self.window = window
            # self.window.state('zoomed')
            self.window.title('Open')
            self.bg_frame = PIL.Image.open('images\\bluestacks_tutorial.png')
            photo = ImageTk.PhotoImage(self.bg_frame)
            self.bg_panel = Label(self.window, image=photo)
            self.bg_panel.image = photo
            self.bg_panel.pack(fill='both', expand='yes')

        def kvk_scan_event():
            kvk_datas_page.kvk_datas(window,expire)



        def open_paypal(e):
            webbrowser.open('https://paypal.me/petruleon?country.x=IT&locale.x=it_IT')
        #try:
        #    expire_in = expire-datetime.today()
        #except:
        #    expire_in = expire
        #expire_in = 'no expires'
        #self.expire_label = Label(self.lgn_frame, text=f'Your license will expire in: {expire_in}',bg="#040405" , fg="#dc143c",
        #                        font=("yu gothic ui", 12, "bold"))
        #self.expire_label.place(x=250, y=550)

        #self.donate_label= Label(self.lgn_frame,text = 'press me', font=("yu gothic ui",8, "bold"),fg = '#bdb9b1', bg = "#040405" )
        #self.donate_label.place(x = 580,y =85)

        self.donate_button = PIL.Image.open('images\\paypal.png')
        donate_photo = ImageTk.PhotoImage(self.donate_button.resize((100,50)))
        self.donate_button_label = Label(self.lgn_frame, image=donate_photo, bg='#040405')
        self.donate_button_label.image = donate_photo
        self.donate_button_label.place(x=295, y=530)

        self.donate_button_label.bind("<Button-1>",open_paypal)

        self.donate = Button(self.donate_button_label, font=("yu gothic ui", 11, "bold"), width=4, bd=0,
                            bg='#bdb9b1', cursor='hand2', activebackground='#bdb9b1', fg='black', command = lambda: open_paypal(1))
        self.donate.place(x=55, y=300)

        ##QUESTION MARKER
        self.question_label= Label(self.lgn_frame,text = 'press me', font=("yu gothic ui",8, "bold"),fg = '#bdb9b1', bg = "#040405" )
        self.question_label.place(x = 580,y =85)

        self.question_button = PIL.Image.open('images\\question_mark.png')
        question_photo = ImageTk.PhotoImage(self.question_button.resize((10,20)))
        self.question_button_label = Label(self.lgn_frame, image=question_photo, bg='#040405')
        self.question_button_label.image = question_photo
        self.question_button_label.place(x=600, y=110)

        self.question_button_label.bind("<Button-1>",open_tutorial)

        self.question = Button(self.question_button_label, font=("yu gothic ui", 11, "bold"), width=4, bd=0,
                            bg='#bdb9b1', cursor='hand2', activebackground='#bdb9b1', fg='black', command = lambda: open_tutorial(1))
        self.question.place(x=55, y=12)


        ##MENU BUTTONS

        self.kd_full_datas_button_menu = Button(self.lgn_frame, text = "Kingdom Datas",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black")
        self.kd_full_datas_button_menu.config(state="disabled", bg = '#bdb9b1', fg = 'black')
        self.kd_full_datas_button_menu.place(x = 0, y = 50)

        self.seed_scan_datas_button_menu = Button(self.lgn_frame, text = "Seed Scan",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command = seed_scan_event)
        self.seed_scan_datas_button_menu.place(x = 150, y = 50)

        self.ally_scan_button_menu = Button(self.lgn_frame, text = "Ally Scan",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command= ally_scan_event)
        self.ally_scan_button_menu.place(x = 302, y = 50)

        self.kvk_datas_button_menu = Button(self.lgn_frame, text = "KVK datas",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black",command = kvk_scan_event)
        self.kvk_datas_button_menu.place(x = 453, y = 50)


        ###START_BUTTON
        self.start_button = PIL.Image.open('images\\btn1.png')
        start_photo = ImageTk.PhotoImage(self.start_button.resize((230,60)))
        self.start_button_label = Label(self.lgn_frame, image=start_photo, bg='#040405')
        self.start_button_label.image = start_photo
        self.start_button_label.place(x=400, y=350)

        self.start = Button(self.start_button_label, text='START', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#C3C3C3', cursor='hand2', activebackground='#C3C3C3', fg='black', command =start)
        self.start.place(x=55, y=12)

        ##contact
        self.cnt_button = PIL.Image.open('images\\contact.png')
        cnt_photo = ImageTk.PhotoImage(self.cnt_button.resize((230,60)))
        self.cnt_button_label = Label(self.lgn_frame, image=cnt_photo, bg='#040405')
        self.cnt_button_label.image = cnt_photo
        self.cnt_button_label.place(x=400, y=415)

        self.contact = Button(self.cnt_button_label, text='CONTACT', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#7F7F7F', cursor='hand2', activebackground='#7F7F7F', fg='#bdb9b1', command= contact_event)
        self.contact.place(x=55, y=12)

        ##STOP BUTTON
        self.stop_button = PIL.Image.open('images\\stop.png')
        stop_photo = ImageTk.PhotoImage(self.stop_button.resize((230,60)))
        self.stop_button_label = Label(self.lgn_frame, image=stop_photo, bg='#040405')
        self.stop_button_label.image = stop_photo
        self.stop_button_label.place(x=400, y=480)

        self.stop = Button(self.stop_button_label, text='STOP', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#292929', cursor='hand2', activebackground='#292929', fg='white', command= stop_event)
        self.stop.place(x=55, y=12)


        d = json.load(open('config.json'))
        ###IP
        self.ip_label = Label(self.lgn_frame, text="IP", bg="#040405", fg="white",
                                    font=("yu gothic ui", 12, "bold"))
        self.ip_label.place(x=50, y=105)



        self.ip_entry = Entry(self.lgn_frame,highlightthickness=0, relief=FLAT, bg="#bdb9b1", fg="#000000",
                                    font=("yu gothic ui ", 12, "bold"))
        self.ip_entry.place(x=150, y=110, width=120)
        self.ip_entry.insert(0, d['ip'])

        self.ip_line = Canvas(self.lgn_frame, width=110, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.ip_line.place(x=33, y=135)


        ###PORT
        self.port_label = Label(self.lgn_frame, text="PORT", bg="#040405", fg="white",
                                    font=("yu gothic ui", 12, "bold"))
        self.port_label.place(x=370, y=105)

        self.port_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#bdb9b1", fg="#000000",
                                    font=("yu gothic ui ", 12, "bold"))
        self.port_entry.place(x=460, y=110, width=120)
        self.port_entry.insert(0, d['port'])
        self.port_line = Canvas(self.lgn_frame, width=110, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.port_line.place(x=343, y=135)

        self.kd_label = Label(self.lgn_frame, text="Kingdom n°", bg="#040405", fg="white",
                                    font=("yu gothic ui", 12, "bold"))
        self.kd_label.place(x=40, y=155)

        self.kd_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#bdb9b1", fg="#000000",
                                    font=("yu gothic ui ", 12, "bold"))
        self.kd_entry.place(x=150, y=160, width=120)

        self.kd_line = Canvas(self.lgn_frame, width=110, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.kd_line.place(x=33, y=185)

        #select ranks
        self.gv_label = Label(self.lgn_frame, text="Governors n°", bg="#040405", fg="white",
                              font=("yu gothic ui", 12, "bold"))
        self.gv_label.place(x=350, y=155)
        options = list(range(5,1005,5))
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TCombobox', fieldbackground = "#bdb9b1", background="#bdb9b1")
        self.gv_entry = ttk.Combobox(self.lgn_frame,font=("yu gothic ui ", 12, "bold"), value = options)
        self.gv_entry.current(0)
        #self.kd_entry.bind("<<ComboboxSelected>>")
        self.gv_entry.place(x=460, y=160, width=120)

        self.kd_line = Canvas(self.lgn_frame, width=115, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.kd_line.place(x=343, y=185)

        self.tier_label = Label(self.lgn_frame, text="Select Tier:", bg="#040405", fg="white",
                              font=("yu gothic ui", 12, "bold"))
        self.tier_label.place(x=60, y=230)


        self.T1= IntVar()
        self.T1_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="T1 kills", variable = self.T1)
        self.T1_Checkbutton.place(x=40, y = 260,width=120)

        self.T2= IntVar()
        self.T2_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="T2 kills", variable = self.T2)
        self.T2_Checkbutton.place(x=40, y = 290,width=120)

        self.T3= IntVar()
        self.T3_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="T3 kills", variable = self.T3)
        self.T3_Checkbutton.place(x=40, y = 320,width=120)

        self.T4= IntVar()
        self.T4_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="T4 kills", variable = self.T4)
        self.T4_Checkbutton.place(x=40, y = 350,width=120)

        self.T5= IntVar()
        self.T5_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="T5 kills", variable = self.T5)
        self.T5_Checkbutton.place(x=40, y = 380,width=120)



        self.kd_line = Canvas(self.lgn_frame, width=2, height=150, bg="#bdb9b1", highlightthickness=0)
        self.kd_line.place(x=170, y=255)

        self.tier_label = Label(self.lgn_frame, text="Other datas:", bg="#040405", fg="white",
                              font=("yu gothic ui", 12, "bold"))
        self.tier_label.place(x=240, y=230)

        self.dead= IntVar()
        self.dead_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="Dead troops", variable = self.dead)
        self.dead_Checkbutton.place(x=220, y = 260,width=120)

        self.rss_assistance= IntVar()
        self.rss_assistance_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="Rss Assistance", variable = self.rss_assistance)
        self.rss_assistance_Checkbutton.place(x=227, y = 290,width=120)

        self.ally_helps= IntVar()
        self.ally_helps_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="ally helps", variable = self.ally_helps)
        self.ally_helps_Checkbutton.place(x=213, y = 320,width=120)

        self.ranged_points= IntVar()
        self.ranged_points_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="ranged points", variable = self.ranged_points)
        self.ranged_points_Checkbutton.place(x=226, y = 350,width=120)


        link = "https://raw.githubusercontent.com/petruleon/test/main/version.txt"
        check = requests.get(link)
        current_path = os.getcwd()
        if float(__version__) < float(check.text):
            mb1 = messagebox.askyesno('Update Available', 'There is an update available. Click yes to update.')
            if mb1 is True:
                print('yes')
                subprocess.Popen([current_path + '\\Updater.exe', '/user:Administrator'])
                self.window.destroy()

