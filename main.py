
from tkinter import *
import GUI_LoginPage as GLP
import GUI_ROKSCAN




def page():
    window = Tk()
    img = PhotoImage(file="icon.png")
    window.iconphoto(False, img)

    # Bypass authentication - go directly to main app
    expire = 'lifetime license'
    username = 'bypass_user'
    password = 'bypass_pass'
    GUI_ROKSCAN.ROKSCAN(window, expire, username, password)

    # Original login page (commented out)
    #GLP.LoginPage(window)

    window.mainloop()


if __name__ == '__main__':
    page()







