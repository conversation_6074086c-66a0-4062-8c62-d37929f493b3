import tkinter as tk
from tkinter import *
from tkinter import ttk
import json
from datetime import datetime

import kvk_datas_page
from keyauth import api
import sys
import os
import hashlib
from Contact import ContactPage
from PIL import ImageTk
import PIL.Image
import threading
from all_info import full_scan
import GUI_ROKSCAN
import Seeding
from time import sleep
import ally_scan_page
import webbrowser

class SeedScan:
    def __init__(self, window,expire):

        def start():
            if not running:
                self.start['state'] = DISABLED
                thread = threading.Thread(target=start_event,daemon=True).start()


        def start_event():
            d = json.load(open('config.json'))
            d['ip'] = self.ip_entry.get()
            d['port'] = self.port_entry.get()
            with open('config.json', 'w') as outfile:
                outfile.write(json.dumps(d, indent=(3)))
            global running
            running = True
            while running:

                IP = self.ip_entry.get()
                PORT = self.port_entry.get()
                KD = self.kd_entry.get()
                gov_num = self.gv_entry.get()
                kraken_var = self.kraken.get()
                Seeding.Seeding(IP,PORT,KD,gov_num, kraken_var)

                new_scan()

        def new_scan():
            self.start['state'] = NORMAL
            global running
            running=False


        def contact_event():
            ContactPage(window)

        def ROKSCAN_event():
            GUI_ROKSCAN.ROKSCAN(window,expire)

        def stop_event():
            Seeding.Stop()
            #new_scan()

        def ally_scan_event():
            ally_scan_page.AllyScan(window,expire)

        def open_tutorial(e):
            window = tk.Toplevel()
            self.window = window
            # self.window.state('zoomed')
            self.window.title('Open')
            self.bg_frame = PIL.Image.open('images\\bluestacks_tutorial.png')
            photo = ImageTk.PhotoImage(self.bg_frame)
            self.bg_panel = Label(self.window, image=photo)
            self.bg_panel.image = photo
            self.bg_panel.pack(fill='both', expand='yes')

        def kvk_scan_event():
            kvk_datas_page.kvk_datas(window, expire)

        self.window = window
        self.window.geometry('800x700')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('ROKStats - App')

        running = False

        self.bg_frame = PIL.Image.open('images\\background1.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        self.lgn_frame = Frame(self.window, bg='#040405', width=650, height=600)
        self.lgn_frame.place(x=80, y=60)



        self.txt = "ROKSTATS"
        self.heading = Label(self.lgn_frame, text=self.txt, font=('yu gothic ui', 25, "bold"), bg="#040405",
                             fg='white',
                             bd=5,
                             relief=FLAT)
        self.heading.place(x=180, y=10, width=300, height=30)

        ##QUESTION MARKER
        self.question_label= Label(self.lgn_frame,text = 'press me', font=("yu gothic ui",8, "bold"),fg = '#bdb9b1', bg = "#040405" )
        self.question_label.place(x = 580,y =85)

        self.question_button = PIL.Image.open('images\\question_mark.png')
        question_photo = ImageTk.PhotoImage(self.question_button.resize((10,20)))
        self.question_button_label = Label(self.lgn_frame, image=question_photo, bg='#040405')
        self.question_button_label.image = question_photo
        self.question_button_label.place(x=600, y=110)

        self.question_button_label.bind("<Button-1>",open_tutorial)

        self.question = Button(self.question_button_label, font=("yu gothic ui", 11, "bold"), width=4, bd=0,
                            bg='#bdb9b1', cursor='hand2', activebackground='#bdb9b1', fg='black', command = lambda: open_tutorial(1))
        self.question.place(x=55, y=12)

        #REAL SEED (if power > 150MLN --> power = 150MLN

        self.kraken= IntVar()
        self.kraken_Checkbutton = Checkbutton(self.lgn_frame,bg='#040405',fg="#bdb9b1",font=("yu gothic ui ", 10, "bold"), text="REAL SEED", variable = self.kraken)
        self.kraken_Checkbutton.place(x=40, y = 370,width=120)

        self.info = Label(self.lgn_frame,font=("yu gothic ui ", 10, "bold"), text = 'if power > 150MLN the counted power\n will be set to 150MLN', bg='#040405',fg="#bdb9b1")
        self.info.place(x = 70, y = 400)

        ##multiple kd

        self.info = Label(self.lgn_frame,font=("yu gothic ui ", 10, "bold"), text = 'In the KD entry field, you can INSERT multiple KDs.\n If you have multiple profiles in different KDs and you want to track their seed\n from now on you can do it.\n The entry field MUST to have the following format: 1111;2222;...;3333,\n KEEP IN MIND: You have to have the profiles in the KD you want to scan.\n This feature is still in BETA.', bg='#040405',fg="#bdb9b1")
        self.info.place(x =40, y = 210)

        ###MENU CANVAS LINES

        self.menu_line = Canvas(self.lgn_frame, width=606, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=75)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=150, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=300, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=452, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=604, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=750, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=50)

        #self.menu_line = Canvas(self.lgn_frame, width=2, height=700, bg="#bdb9b1", highlightthickness=0)
        #self.menu_line.place(x=648, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=650, height=2, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=598)

        #try:
        #    expire_in = expire-datetime.today()
        #except:
        #    expire_in = expire
        #self.expire_label = Label(self.lgn_frame, text=f'Your license will expire in: {expire_in}',bg="#040405" , fg="#dc143c",
        #                        font=("yu gothic ui", 12, "bold"))
        #self.expire_label.place(x=250, y=550)

        def open_paypal(e):
            webbrowser.open('https://paypal.me/petruleon?country.x=IT&locale.x=it_IT')
        self.donate_button = PIL.Image.open('images\\paypal.png')
        donate_photo = ImageTk.PhotoImage(self.donate_button.resize((100,50)))
        self.donate_button_label = Label(self.lgn_frame, image=donate_photo, bg='#040405')
        self.donate_button_label.image = donate_photo
        self.donate_button_label.place(x=295, y=530)

        self.donate_button_label.bind("<Button-1>",open_paypal)

        self.donate = Button(self.donate_button_label, font=("yu gothic ui", 11, "bold"), width=4, bd=0,
                            bg='#bdb9b1', cursor='hand2', activebackground='#bdb9b1', fg='black', command = lambda: open_paypal(1))
        self.donate.place(x=55, y=300)

        ### MENU BUTTONS
        self.kd_full_datas_button_menu = Button(self.lgn_frame, text = "Kingdom Datas",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command = ROKSCAN_event)
        self.kd_full_datas_button_menu.place(x = 0, y = 50)

        self.seed_scan_datas_button_menu = Button(self.lgn_frame, text = "Seed Scan",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black")
        self.seed_scan_datas_button_menu.config(state="disabled", bg='#bdb9b1', fg='black')
        self.seed_scan_datas_button_menu.place(x = 150, y = 50)

        self.ally_scan_button_menu = Button(self.lgn_frame, text = "Ally Scan",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command = ally_scan_event)
        self.ally_scan_button_menu.place(x = 302, y = 50)

        self.kvk_datas_button_menu = Button(self.lgn_frame, text = "KVK datas",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command = kvk_scan_event)
        self.kvk_datas_button_menu.place(x = 453, y = 50)



        ###START_BUTTON
        self.start_button = PIL.Image.open('images\\btn1.png')
        start_photo = ImageTk.PhotoImage(self.start_button.resize((230,60)))
        self.start_button_label = Label(self.lgn_frame, image=start_photo, bg='#040405')
        self.start_button_label.image = start_photo
        self.start_button_label.place(x=400, y=350)

        self.start = Button(self.start_button_label, text='START', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#c3c3c3', cursor='hand2', activebackground='#c3c3c3', fg='black', command =start)
        self.start.place(x=55, y=12)

        ##contact
        self.cnt_button = PIL.Image.open('images\\contact.png')
        cnt_photo = ImageTk.PhotoImage(self.cnt_button.resize((230,60)))
        self.cnt_button_label = Label(self.lgn_frame, image=cnt_photo, bg='#040405')
        self.cnt_button_label.image = cnt_photo
        self.cnt_button_label.place(x=400, y=415)

        self.contact = Button(self.cnt_button_label, text='CONTACT', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#7f7f7f', cursor='hand2', activebackground='#7f7f7f', fg='#bdb9b1', command= contact_event)
        self.contact.place(x=55, y=12)

        ##STOP BUTTON
        self.stop_button = PIL.Image.open('images\\stop.png')
        stop_photo = ImageTk.PhotoImage(self.stop_button.resize((230,60)))
        self.stop_button_label = Label(self.lgn_frame, image=stop_photo, bg='#040405')
        self.stop_button_label.image = stop_photo
        self.stop_button_label.place(x=400, y=480)

        self.stop = Button(self.stop_button_label, text='STOP', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#292929', cursor='hand2', activebackground='#292929', fg='white', command= stop_event)
        self.stop.place(x=55, y=12)

        d = json.load(open('config.json'))
        ###IP
        self.ip_label = Label(self.lgn_frame, text="Check IP", bg="#040405", fg="white",
                                    font=("yu gothic ui", 12, "bold"))
        self.ip_label.place(x=50, y=105)



        self.ip_entry = Entry(self.lgn_frame,highlightthickness=0, relief=FLAT, bg="#bdb9b1", fg="#000000",
                                    font=("yu gothic ui ", 12, "bold"))
        self.ip_entry.place(x=150, y=110, width=120)
        self.ip_entry.insert(0, d['ip'])

        self.ip_line = Canvas(self.lgn_frame, width=110, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.ip_line.place(x=33, y=135)


        ###PORT
        self.port_label = Label(self.lgn_frame, text="PORT", bg="#040405", fg="white",
                                    font=("yu gothic ui", 12, "bold"))
        self.port_label.place(x=370, y=105)

        self.port_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#bdb9b1", fg="#000000",
                                    font=("yu gothic ui ", 12, "bold"))
        self.port_entry.place(x=460, y=110, width=120)
        self.port_entry.insert(0, d['port'])
        self.port_line = Canvas(self.lgn_frame, width=110, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.port_line.place(x=343, y=135)

        self.kd_label = Label(self.lgn_frame, text="Kingdom n°", bg="#040405", fg="white",
                                    font=("yu gothic ui", 12, "bold"))
        self.kd_label.place(x=40, y=155)

        self.kd_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#bdb9b1", fg="#000000",
                                    font=("yu gothic ui ", 12, "bold"))
        self.kd_entry.place(x=150, y=160, width=120)

        self.kd_line = Canvas(self.lgn_frame, width=110, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.kd_line.place(x=33, y=185)


        ##GOV_NUM

        self.gv_label = Label(self.lgn_frame, text="Governors n°", bg="#040405", fg="white",
                              font=("yu gothic ui", 12, "bold"))
        self.gv_label.place(x=350, y=155)
        options = list(range(100,1005,100))
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TCombobox', fieldbackground = "#bdb9b1", background="#bdb9b1")
        self.gv_entry = ttk.Combobox(self.lgn_frame,font=("yu gothic ui ", 12, "bold"), value = options)
        self.gv_entry.current(0)
        self.gv_entry.place(x=460, y=160, width=120)

        self.kd_line = Canvas(self.lgn_frame, width=115, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.kd_line.place(x=343, y=185)

