import tkinter as tk
from tkinter import *
from tkinter import ttk
import json
from datetime import datetime
from keyauth import api
import sys

import os
import hashlib
#from GUI_ROKSCAN import ROKSCAN
from PIL import ImageTk
import PIL.Image
import tkinter.messagebox
import webbrowser

class ContactPage:
    def __init__(self, window):
        window = tk.Toplevel()
        self.window = window
        self.window.geometry('555x260')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('Contacts')
        self.bg_frame = PIL.Image.open('images\\contact_background.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        def open_discord(e):
            webbrowser.open_new('https://discord.gg/ZuxV4eRBTZ')

        def open_facebook(e):
            webbrowser.open_new('https://www.facebook.com/profile.php?id=100088591805514')

        def open_email(e):
            tkinter.messagebox.showinfo(title = "E-mail", message="E-mail still work in progress",parent=window)


        self.lgn_frame = Frame(self.window, bg='#151E2F', width=350, height=150)
        self.lgn_frame.place(x=110, y=50)

        self.discord = PIL.Image.open('images\\discord-logo.png')
        discord_photo = ImageTk.PhotoImage(self.discord.resize((100,100)))
        self.discord_button_label = Label(self.lgn_frame, image=discord_photo,bg='#151E2F')
        self.discord_button_label.image = discord_photo
        self.discord_button_label.place(x=20, y=15)

        self.discord_button_label.bind("<Button-1>",open_discord)

        self.discord_button = Button(self.lgn_frame, text='DISCORD', font=("yu gothic ui", 10, "bold"), width=12, bd=0,
                            bg='#151E2F', cursor='hand2', activebackground='#151E2F', fg='white', command= lambda : open_discord(1))
        self.discord_button.place(x=20, y=100)

        self.facebook = PIL.Image.open('images\\facebook-logo.png')
        facebook_photo = ImageTk.PhotoImage(self.facebook.resize((80,80)))
        self.facebook_button_label = Label(self.lgn_frame, image=facebook_photo,bg='#151E2F')
        self.facebook_button_label.image = facebook_photo
        self.facebook_button_label.place(x=150, y=15)
        self.facebook_button_label.bind("<Button-1>", open_facebook)

        self.facebook_button = Button(self.lgn_frame, text='FACEBOOK', font=("yu gothic ui", 10, "bold"), width=12, bd=0,
                                  bg='#151E2F', cursor='hand2', activebackground='#151E2F', fg='white', command = lambda : open_facebook(2))
        self.facebook_button.place(x=140, y=100)

        self.email = PIL.Image.open('images\\email-logo.png')
        email_photo = ImageTk.PhotoImage(self.email.resize((60,60)))
        self.email_button_label = Label(self.lgn_frame, image=email_photo,bg='#151E2F')
        self.email_button_label.image = email_photo
        self.email_button_label.place(x=270, y=25)
        self.email_button_label.bind("<Button-1>", open_email)

        self.email_button = Button(self.lgn_frame, text='E-MAIL', font=("yu gothic ui", 10, "bold"), width=10, bd=0,  bg='#151E2F', cursor='hand2', activebackground='#151E2F', fg='white', command = lambda: open_email(1))
        self.email_button.place(x=260, y=100)