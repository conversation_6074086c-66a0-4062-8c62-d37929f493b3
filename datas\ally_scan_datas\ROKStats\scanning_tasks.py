import numpy
from PIL import Image, ImageEnhance
import pytesseract
import subprocess
import io
import PIL.Image
from time import sleep
import cv2
import numpy as np
from tkinter import messagebox



def tap(x,y, n, IP,PORT):
    adb_path = 'adb\\adb.exe'
    for i in range(n):
        subprocess.run(f'{adb_path} -s {str(IP).strip()}:{PORT} shell input tap {x} {y}', stderr=subprocess.PIPE)
    return sleep(0.5)

def Screenshot(IP,PORT):
    adb_path = 'adb\\adb.exe'
    screenshot = subprocess.run(f'{adb_path} -s {str(IP).strip()}:{PORT} exec-out screencap -p',stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return PIL.Image.open(io.BytesIO(screenshot.stdout))

def inizialize(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    while ready == None:
        screenshot = Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/in_city.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        target1 = cv2.imread('images/out_of_city.png', 0)
        h, w = target.shape
        result1 = cv2.matchTemplate(screenshot, target1, cv2.TM_CCOEFF_NORMED)
        min_val1, max_val1, min_loc1, max_loc1 = cv2.minMaxLoc(result1)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location1 = min_loc1
        else:
            location1 = max_loc1

        if max_val < 0.7 and max_val1 < 0.7:
            sleep(1)
            subprocess.run(f'{adb_path} -s {str(IP).strip()}:{PORT} shell input keyevent 4', stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE)
            ready = None
        else:
            if max_val1 > 0.7:
                tap(location1[0],location1[1],1,IP,PORT)
            ready = True
        sleep(1)
    sleep(1)




def start_scanning(IP,PORT):
    tap(50, 38, 1, IP,PORT) #profile tap
    tap(444, 532, 1, IP,PORT) # rankings tap
    tap(350, 420, 1, IP,PORT) #individual power ranking tap


def scan_id(profile_image, IP, PORT):
    id_box = (618, 187, 730, 211)
    id_image = profile_image.crop(id_box)
    img_gray = cv2.cvtColor(numpy.uint8(id_image), cv2.COLOR_BGR2GRAY)
    id = pytesseract.image_to_string(img_gray, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    counter = 0
    while id == '' and counter <= 5:
        sleep(1)
        new_screenshot = Screenshot(IP,PORT)
        id_box = (617,187,710,213)
        new_id_image = new_screenshot.crop(id_box)
        print('first attempt empty')
        cv2.imwrite(f'temp.png', np.uint8(new_id_image))
        re_open = Image.open(f'temp.png').convert('L')
        enhancer = ImageEnhance.Contrast(re_open)
        re_open = enhancer.enhance(15.0)
        id = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1
    if id == '':
        print('still empty')
    return id

def scan_name(profile_image):
    name_box = (512, 210, 720, 250)
    name_image = profile_image.crop(name_box)
    img_gray = cv2.cvtColor(numpy.uint8(name_image), cv2.COLOR_BGR2GRAY)
    name = pytesseract.image_to_string(img_gray).replace('\n', '')
    return name

def scan_power(profile_image, IP,PORT):
    power_box = (725, 294, 862, 322)
    power_image = profile_image.crop(power_box)
    #img_gray = cv2.cvtColor(numpy.uint8(power_image), cv2.COLOR_BGR2GRAY)
    #power = pytesseract.image_to_string(img_gray, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
    counter = 0
    power= ''
    while power == '' and counter <= 5:
        new_power_image = profile_image.crop(power_box)
        cv2.imwrite(f'temp_power.png', numpy.uint8(new_power_image))
        re_open = PIL.Image.open(f'temp_power.png').convert('L')
        power = pytesseract.image_to_string(re_open,
                                            config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter +=1
    return int(power)

def scan_alliance(profile_image):
    alliance_box = (513, 290, 708, 321)
    alliance_image = profile_image.crop(alliance_box)
    img_gray_ally = cv2.cvtColor(numpy.uint8(alliance_image), cv2.COLOR_BGR2GRAY)
    alliance = pytesseract.image_to_string(img_gray_ally).replace('\n', '')
    return alliance

def scan_kill_points(kill_points_info, IP, PORT):
    kill_points_box = (907, 293, 1077, 317)
    #kill_points_image = kill_points_info.crop(kill_points_box)
    #img_gray_kp = cv2.cvtColor(numpy.uint8(kill_points_image), cv2.COLOR_BGR2GRAY)
    #kill_points = pytesseract.image_to_string(img_gray_kp, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
    counter = 0
    kill_points = ''
    while kill_points == '' and counter <= 5:
        screenshot = kill_points_info
        new_power_image = screenshot.crop(kill_points_box)
        cv2.imwrite(f'temp_kp.png', numpy.uint8(new_power_image))
        re_open = PIL.Image.open(f'temp_kp.png').convert('L')
        kill_points = pytesseract.image_to_string(re_open,
                                            config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter +=1
    try:
        kill_points = int(kill_points)
    except:
        kill_points = kill_points
    return kill_points

def scan_tier_kills(tier_screenshot, IP,PORT):
    #tier_kills_box = (678, 477, 913, 640)
    t1_box = (689,368,794,396)
    t2_box = (689,403,794,430)
    t3_box = (689,442,794,464)
    t4_box = (689,475,794,501)
    t5_box = (689,512,794,536)
    ranged_points_box = (927,595,1133,620)



    #ranged_points_image = kill_points_info.crop(ranged_points_box)
    #img_gray_ranged_points = cv2.cvtColor(numpy.uint8(ranged_points_image), cv2.COLOR_BGR2GRAY)
    #ranged_points = pytesseract.image_to_string(img_gray_ranged_points, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    ranged_points = ''
    counter = 0
    while ranged_points == '' and counter <= 5:
        sleep(1)
        sc = tier_screenshot
        ranged_points_image = sc.crop(ranged_points_box)
        cv2.imwrite(f'temp.png', np.uint8(ranged_points_image))
        re_open = Image.open(f'temp.png').convert('L')
        #enhancer = ImageEnhance.Contrast(re_open)
        #re_open = enhancer.enhance(15.0)
        ranged_points = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    #t1_image = kill_points_info.crop(t1_box)
    #img_gray_t1 = cv2.cvtColor(numpy.uint8(t1_image), cv2.COLOR_BGR2GRAY)
    #t1 = pytesseract.image_to_string(img_gray_t1, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    t1 = ''
    counter = 0
    while t1 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t1_image = sc.crop(t1_box)
        cv2.imwrite(f'temp.png', np.uint8(t1_image))
        re_open = Image.open(f'temp.png').convert('L')
        #enhancer = ImageEnhance.Contrast(re_open)
        #re_open = enhancer.enhance(15.0)
        t1 = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    #t2_image = kill_points_info.crop(t2_box)
    #img_gray_t2 = cv2.cvtColor(numpy.uint8(t2_image), cv2.COLOR_BGR2GRAY)
    #t2 = pytesseract.image_to_string(img_gray_t2, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    t2 = ''
    counter = 0
    while t2 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t2_image = sc.crop(t2_box)
        cv2.imwrite(f'temp.png', np.uint8(t2_image))
        re_open = Image.open(f'temp.png').convert('L')
        #enhancer = ImageEnhance.Contrast(re_open)
        #re_open = enhancer.enhance(15.0)
        t2 = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    #t3_image = kill_points_info.crop(t3_box)
    #img_gray_t3 = cv2.cvtColor(numpy.uint8(t3_image), cv2.COLOR_BGR2GRAY)
    #t3 = pytesseract.image_to_string(img_gray_t3, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    t3 = ''
    counter = 0
    while t3 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t3_image = sc.crop(t3_box)
        cv2.imwrite(f'temp.png', np.uint8(t3_image))
        re_open = Image.open(f'temp.png').convert('L')
        #enhancer = ImageEnhance.Contrast(re_open)
        #re_open = enhancer.enhance(15.0)
        t3 = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    #t4_image = kill_points_info.crop(t4_box)
    #img_gray_t4 = cv2.cvtColor(numpy.uint8(t4_image), cv2.COLOR_BGR2GRAY)
    #t4 = pytesseract.image_to_string(img_gray_t4, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    t4 = ''
    counter = 0
    while t4 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t4_image = sc.crop(t4_box)
        cv2.imwrite(f'temp.png', np.uint8(t4_image))
        re_open = Image.open(f'temp.png').convert('L')
        #enhancer = ImageEnhance.Contrast(re_open)
        #re_open = enhancer.enhance(15.0)
        t4 = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1

    #t5_image = kill_points_info.crop(t5_box)
    #img_gray_t5 = cv2.cvtColor(numpy.uint8(t5_image), cv2.COLOR_BGR2GRAY)
    #t5 = pytesseract.image_to_string(img_gray_t5, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    t5 = ''
    counter = 0
    while t5 == '' and counter <= 5:
        #sleep(1)
        sc = tier_screenshot
        t5_image = sc.crop(t5_box)
        cv2.imwrite(f'temp.png', np.uint8(t5_image))
        re_open = Image.open(f'temp.png').convert('L')
        #enhancer = ImageEnhance.Contrast(re_open)
        #re_open = enhancer.enhance(15.0)
        t5 = pytesseract.image_to_string(re_open, config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
        counter += 1


    tier_kills_prov = [t1,t2,t3,t4,t5, ranged_points]
    tier_kills = []
    for i in tier_kills_prov:
        try:
            tier_kills.append(int(i))
        except:
            if i == '':
                tier_kills.append(0)
            else:
                tier_kills.append(i)
    return tier_kills

def scan_deaths(more_info_image):
    deaths_box = (900, 357, 1065, 389)
    deaths = ''
    counter = 0
    while deaths == '' and counter <= 5:
        deaths_image = more_info_image.crop(deaths_box)
        img_gray_deaths = cv2.cvtColor(numpy.uint8(deaths_image), cv2.COLOR_BGR2GRAY)
        cv2.imwrite(f'temp_deaths.png', np.uint8(img_gray_deaths))
        re_open = cv2.imread('temp_deaths.png')
        (h, w) = re_open.shape[:2]
        re_open = cv2.resize(re_open, (w * 3, h * 3))
        cv2.imwrite('re_open.png', np.uint8(re_open))
        deaths = pytesseract.image_to_string(re_open, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
        #
        counter += 1
    try:
        deaths = int(deaths)
    except:
        deaths = deaths
    return deaths

def scan_ally_helps(more_info_image):
    ally_helps_box = (950, 590, 1050, 620)
    ally_helps = ''
    counter = 0
    while ally_helps == '' and counter <= 5:
        ally_helps_image = more_info_image.crop(ally_helps_box)
        #img_gray_ally_helps = cv2.cvtColor(numpy.uint8(ally_helps_image), cv2.COLOR_BGR2GRAY)
        cv2.imwrite(f'temp_helps.png', np.uint8(ally_helps_image))
        re_open = Image.open(f'temp_helps.png').convert('L')
        ally_helps = pytesseract.image_to_string(re_open, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
        counter += 1
    try:
        ally_helps = int(ally_helps)
    except:
        ally_helps = ally_helps

    return ally_helps



def scan_rss_assistance(more_info_image):
    rss_assistance_box = (848, 537, 1052, 571)
    rss_assistance_image = more_info_image.crop(rss_assistance_box)
    img_gray_rss = cv2.cvtColor(numpy.uint8(rss_assistance_image), cv2.COLOR_BGR2GRAY)
    rss_assistance = pytesseract.image_to_string(img_gray_rss, config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789').replace('\n', '').replace(',','')
    try:
        rss_assistance = int(rss_assistance)
    except:
        rss_assistance = rss_assistance
    return rss_assistance

def scan_real_name(IP,PORT):
    adb_path = 'adb\\adb.exe'
    tap(298, 124, 1, IP,PORT)  # tapping on copy_name

    log = subprocess.run(f'{adb_path} -s {str(IP).strip()}:{PORT} shell logcat -d', stdout=subprocess.PIPE,
                         stderr=subprocess.PIPE, encoding='utf-8')
    sub1 = 'hcallSetClipboardTextRpc('
    pos1 = str(log.stdout).find(sub1)
    partially_cleaned = str(log)[pos1:]
    sub2 = ')'
    pos2 = partially_cleaned.find(sub2)
    cleaned_name = partially_cleaned[166:pos2]
    sub3 = 'hcallSetClipboardTextRpc('
    pos3 = cleaned_name.find(sub3)
    real_name = cleaned_name[pos3 + 25:]
    return real_name

def clear_logs(IP,PORT):
    adb_path = 'adb\\adb.exe'
    subprocess.run(f'{adb_path} -s {str(IP).strip()}:{PORT} shell logcat -b all -c', stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

def check_profile(kd_num, IP, PORT):
    pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"
    adb_path = 'adb\\adb.exe'
    ###building kd img
    imgs = [Image.open(f'images/kd_numbers/{i}.png') for i in kd_num]

    min_shape = sorted([(np.sum(i.size), i.size) for i in imgs])[0][1]
    imgs_comb = np.hstack([i.resize(min_shape) for i in imgs])
    imgs_comb = Image.fromarray(imgs_comb)
    cv2.imwrite('target.png', np.uint8(imgs_comb))
    tap(65,655,1,IP,PORT)
    tap(304, 22, 1, IP, PORT)
    tap(435, 142, 1, IP, PORT)
    check_kd_screenshot = Screenshot(IP,PORT)
    current_kd_box = (6, 641, 98, 696)
    current_kd_image = check_kd_screenshot.crop(current_kd_box)
    img_gray = cv2.cvtColor(numpy.uint8(current_kd_image), cv2.COLOR_BGR2GRAY)
    current_kd = pytesseract.image_to_string(img_gray,config='--psm 13 --oem 3 -c tessedit_char_whitelist=0123456789')
    if current_kd == kd_num:
        return
    tap(50, 38, 1, IP, PORT)
    tap(50, 38, 1, IP, PORT)
    tap(50, 38, 1, IP, PORT)# profile tap
    sleep(1)
    tap(990,530,1,IP, PORT) #tap on settings
    sleep(1)
    tap(345, 370, 1, IP, PORT)  # tap on profile management
    sleep(2)
    found = False
    run_time = 0
    while found == False:
        if run_time <= 5:
            run_time += 1
            searching = Screenshot(IP,PORT)
            screenshot = cv2.cvtColor(np.array(searching), cv2.COLOR_RGB2GRAY)
            target = cv2.imread('target.png',0)
            print(target.shape)
            h, w = target.shape
            result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            print(cv2.minMaxLoc(result))
            if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_CCOEFF_NORMED]:
                location = min_loc
            else:
                location = max_loc
            threshold = 0.90
            loc = np.where(result >= threshold)
            for pt in zip(*loc[::-1]):  # Switch collumns and rows
                print(pt[0])
                tap(pt[0], pt[1], 1, IP, PORT)
                tap(810,509, 1, IP, PORT)
                found = True
                feedback = True
                return feedback

            if max_val < 0.90:
                print('none')
                sleep(1)
                subprocess.run(f'{adb_path} -s {str(IP).strip()}:{PORT} shell input swipe 640 395 640 262 2000',
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE)
        else:
            messagebox.showinfo(title="Error",
                                        message=f'I didnt find the kingdom number {kd_num} between your profiles, please check if the profile does exists or contact ROKStats')
            feedback = False
            return  feedback







