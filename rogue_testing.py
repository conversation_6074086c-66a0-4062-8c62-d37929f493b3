import subprocess
from time import sleep
import cv2
import subprocess
import numpy as np
from time import sleep
import io
import PIL.Image
import numpy
from PIL import Image, ImageEnhance,ImageGrab
#import solve_captcha
import random
import json
import math
import pytesseract

pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"


adb_path = 'adb\\adb.exe'
#adb.bridge = adb.enable_adb()
subprocess.run([f'{adb_path}', '-P', '5038', 'start-server'])
IP_PORT = str('127.0.0.1') + ':'+ str('5865')
IP = "127.0.0.1"
PORT = "5865"
connection = subprocess.run(f'{adb_path} -P 5038 connect {IP_PORT}', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
def calculate_distance(point1, point2):
    return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)


def pinch_screen():
    adb_command = f"{adb_path} -P 5038 -s {str('127.0.0.1').strip()}:{'5865'} shell sh /sdcard/sendevent_input.sh"
    subprocess.run(adb_command, shell=True)
    sleep(1)

def tap(x,y, n, IP,PORT):
    adb_path = 'adb\\adb.exe'
    for i in range(n):
        subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}', stderr=subprocess.PIPE)
        sleep(1)
    return

def Screenshot(IP,PORT):
    adb_path = 'adb\\adb.exe'
    max_retries = 10
    for i in range(max_retries):
        screenshot = subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} exec-out screencap -p',
                                    stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        try:
            #cv2.imwrite(f'screenshot.png', np.uint8(screenshot.stdout))
            #re_open = PIL.Image.open(f'screenshot.png')
            return PIL.Image.open(io.BytesIO(screenshot.stdout))

        except PIL.UnidentifiedImageError:
            print(f"Failed to capture screenshot on attempt {i + 1}/{max_retries}")
    raise Exception("Failed to capture screenshot after retries.")

def check_sqr_distance(city_icons_location, loc):
    for found_icon_loc in city_icons_location:
        distance = calculate_distance(found_icon_loc, loc)
        if distance < 20:
            return True

def swipes(IP,PORT):
    steps = 0
    cycle_number = 0
    # tutta la mappa 1200x1200
    y_r = 90
    x_b = 90
    y_l = 92
    x_t = 89
    # y_r = 3
    # x_b = 3
    # y_l = 3
    # x_t = 2
    counter_y_r = 1
    counter_x_b = 1
    counter_y_l = 1
    counter_x_t = 1
    path_completed = False
    while path_completed is not True:

        steps += 1
        if counter_y_r <= y_r:
            print(f'Bottom {counter_y_r}')
            counter_y_r += 1
            subprocess.run(
                f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 616 569 616 76 3000",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE)
        elif counter_x_b <= x_b:
            print(f'Left {counter_x_b}')
            counter_x_b += 1
            subprocess.run(
                f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 220 370 1002 370 3000",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE)
        elif counter_y_l <= y_l:
            print(f'Upper {counter_y_l}')
            counter_y_l += 1
            subprocess.run(
                f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 616 76 616 569 3000",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE)
        elif counter_x_t <= x_t:
            print(f'Right {counter_x_t}')
            counter_x_t += 1
            subprocess.run(
                f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 1002 370 220 370 3000",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE)

        else:
            cycle_number += 1
            if cycle_number == 1:
                y_r = y_r - 1
                x_b = x_b - 2
                y_l = y_l - 2
                x_t = x_t - 2
            elif cycle_number > 1:
                y_r = y_r - 2
                x_b = x_b - 2
                y_l = y_l - 2
                x_t = x_t - 2
            counter_y_r = 1
            counter_x_b = 1
            counter_y_l = 1
            counter_x_t = 1
            print(
                f'Cycle {cycle_number} completed! New steps to be done: bottom {y_r}, left {x_b}, upper {y_l}, right {x_t} ')
            if x_t + 1 < 0:
                path_completed = True

        #print(steps)

        # Check for cities
        check_for_cities(IP,PORT)

def check_for_cities(IP, PORT):

    back_to_city_icon = cv2.imread('images/back_to_city.png', 0)
    screenshot = Screenshot(IP, PORT)
    screenshot_gray = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)

    # Check for the back_to_city icon
    result_back_to_city = cv2.matchTemplate(screenshot_gray, back_to_city_icon, cv2.TM_CCOEFF_NORMED)
    _, max_val_back_to_city, _, max_loc_back_to_city = cv2.minMaxLoc(result_back_to_city)

    #print("Back to City Icon Confidence:", max_val_back_to_city)
    if max_val_back_to_city > 0.8:
        # Draw a solid rectangle around the found back_to_city icon
        w, h = back_to_city_icon.shape[::-1]
        bottom_right = (max_loc_back_to_city[0] + w, max_loc_back_to_city[1] + h)
        cv2.rectangle(screenshot_gray, max_loc_back_to_city, bottom_right, 255, -1)
        city_icons = ['images/city_icon.png','images/city_icon1.png','images/city_icon2.png','images/city_icon3.png','images/city_icon4.png']

        # List to store the locations of found city icons
        city_icon_locations = []
        # Search for the city_icon in the same screenshot
        for i in city_icons:
            city_icon = cv2.imread(i, 0)
            result_city = cv2.matchTemplate(screenshot_gray, city_icon, cv2.TM_CCOEFF_NORMED)
            _, val_city, _, loc_city = cv2.minMaxLoc(result_city)

            if val_city > 0.9:
                # Store the location if confidence is high enough
                #calculate the sqr distance between the found value and the others in the list
                is_similar = check_sqr_distance(city_icon_locations, loc_city)
                if loc_city not in city_icon_locations and is_similar is not True:
                    city_icon_locations.append(loc_city)

        for loc in city_icon_locations:
            print('City icon found at:', loc)
            sleep(1)
            ##take note about coords before clicking
            coords_dict = check_coords(IP,PORT)

            #tap(loc[0], loc[1], 1, IP, PORT)
            # Simulate checking_id_func
            print('Simulating checking_id_func')
            sleep(5)

            ##go back to coords

        #pinch_screen()

def go_to_coords(IP,PORT, coords_dict):
    x = 340
    y = 19
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    # tap on kingdom number for input
    x = 444
    y = 142
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)

    # cleanin kingdom number input
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent KEYCODE_MOVE_END",
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

    subprocess.run(
        f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL",
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE)

    # input kingdom number
    print('Cleaning entry boxes')
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {coords_dict['kd']}",
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

    # tap on X coordinates  for input

    x = 637
    y = 142
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(0.1)
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)

    # input x coordinate
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {coords_dict['x']}",
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)
    sleep(1)

    # tap on y coordinate for input
    x = 778
    y = 140
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(0.1)
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)

    # input y coordinate
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {coords_dict['y']}",
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)
    sleep(1)
    # tap on go to coordinates
    x = 885
    y = 143
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(0.1)
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(1)

def modify_image(re_open):
    img = cv2.resize(re_open, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
    # Convert the image to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Apply a threshold to the image to make the text more visible
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]
    # Perform some morphological operations to remove noise and smooth the image
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
    dilate = cv2.dilate(opening, kernel, iterations=1)
    return dilate
def check_coords(IP,PORT):
    ##tesseract read coords
    screenshot = Screenshot(IP,PORT)
    coords_box = (285, 11, 425, 31)
    id_image = screenshot.crop(coords_box)
    img_gray = cv2.cvtColor(numpy.uint8(id_image), cv2.COLOR_BGR2GRAY)
    cv2.imwrite(f'temp.png', numpy.uint8(img_gray))
    re_open = cv2.imread('temp.png')
    dilate = modify_image(re_open)
    cv2.imwrite(f'temp1.png', numpy.uint8(dilate))
    coords_string = pytesseract.image_to_string(dilate) #config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789'
    print(coords_string)
    result_dict = clear_coords(coords_string)
    return result_dict

def clear_coords(coords_string):
    kd_max_chars = 4
    x_max_chars = 4
    y_max_chars = 4

    split_values = coords_string.split()
    kd_value = int(split_values[0])  # Convert to integer
    x_value = int(split_values[1].split('X:')[1])  # Extract the X value and convert to integer
    y_value = int(split_values[2].split('Y:')[1])  # Extract the Y value and convert to integer

    # Create a dictionary
    result_dict = {'kd': kd_value, 'x': x_value, 'y': y_value}
    return result_dict
#print(check_coords(IP,PORT))


def start():
    #2122 1162 1168
    kd_numb = 2122
    x_dest = 1180
    y_dest = 1180
    #x_dest = '1160'
    #y_dest = '1180'
    coords_dict = {'kd': kd_numb, 'x': x_dest, 'y': y_dest}
    # Example usage
    pinch_screen()
    x = 340
    y = 19
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    # tap on kingdom number for input
    x = 444
    y = 142
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)

    # cleanin kingdom number input
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent KEYCODE_MOVE_END",
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

    subprocess.run(
        f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL",
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE)

    # input kingdom number
    print('Cleaning entry boxes')
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {kd_numb}",
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

    # tap on X coordinates  for input

    x = 637
    y = 142
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(0.1)
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)


    # input x coordinate
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {x_dest}", stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)
    sleep(2)

    # tap on y coordinate for input
    x = 778
    y = 140
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(0.1)
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)

    # input y coordinate
    subprocess.run(f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {y_dest}", stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)
    sleep(2)

    # tap on go to coordinates
    x = 885
    y = 143
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(0.1)
    adb_command = f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}"
    subprocess.run(adb_command, shell=True)
    sleep(5)

    swipes1(IP,PORT, coords_dict)

def swipes1(IP,PORT,coords_dict):
    steps = 0
    cycle_number = 0
    # tutta la mappa 1200x1200
    #y_r = 90
    #x_b = 90
    #y_l = 92
    #x_t = 89
    y_r = 3
    x_b = 3
    y_l = 3
    x_t = 2
    counter_y_r = 1
    counter_x_b = 1
    counter_y_l = 1
    counter_x_t = 1
    path_completed = False
    while path_completed is not True:

        steps += 1
        if counter_y_r <= y_r:
            print(f'Bottom {counter_y_r}')
            counter_y_r += 1

            coords_dict['y'] = coords_dict['y'] - 20
            go_to_coords(IP,PORT, coords_dict)
            #subprocess.run(
            #    f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 616 569 616 76 3000",
            #    stdout=subprocess.PIPE,
            #    stderr=subprocess.PIPE)
        elif counter_x_b <= x_b:
            print(f'Left {counter_x_b}')
            counter_x_b += 1
            coords_dict['x'] = coords_dict['x'] - 20
            go_to_coords(IP, PORT, coords_dict)
            #subprocess.run(
            #    f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 220 370 1002 370 3000",
            #    stdout=subprocess.PIPE,
            #    stderr=subprocess.PIPE)
        elif counter_y_l <= y_l:
            print(f'Upper {counter_y_l}')
            counter_y_l += 1
            coords_dict['y'] = coords_dict['y'] + 20
            go_to_coords(IP, PORT, coords_dict)
            #subprocess.run(
            #    f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 616 76 616 569 3000",
            #    stdout=subprocess.PIPE,
            #    stderr=subprocess.PIPE)
        elif counter_x_t <= x_t:
            print(f'Right {counter_x_t}')
            counter_x_t += 1
            coords_dict['x'] = coords_dict['x'] + 20
            go_to_coords(IP, PORT, coords_dict)
            #subprocess.run(
            #    f"{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 1002 370 220 370 3000",
            #    stdout=subprocess.PIPE,
            #    stderr=subprocess.PIPE)

        else:
            cycle_number += 1
            if cycle_number == 1:
                y_r = y_r - 1
                x_b = x_b - 2
                y_l = y_l - 2
                x_t = x_t - 2
            elif cycle_number > 1:
                y_r = y_r - 2
                x_b = x_b - 2
                y_l = y_l - 2
                x_t = x_t - 2
            counter_y_r = 1
            counter_x_b = 1
            counter_y_l = 1
            counter_x_t = 1
            print(
                f'Cycle {cycle_number} completed! New steps to be done: bottom {y_r}, left {x_b}, upper {y_l}, right {x_t} ')
            if x_t + 1 < 0:
                path_completed = True

        #print(steps)
start()