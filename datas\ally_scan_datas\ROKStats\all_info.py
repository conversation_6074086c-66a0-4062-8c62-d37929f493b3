import pytesseract
import adb
import tkinter
import subprocess
import tkinter.messagebox
import scanning_tasks as st
import time
import pandas as pd
from datetime import datetime
from datetime import date
from time import sleep
from Seeding import am_i_working_seeding
import File_pops
import Ally_script
from openpyxl import load_workbook
import cv2
import numpy as np

def am_i_working_all_info():
    global im_working_all_info
    try:
        if im_working_all_info == True:
            return True
        else:
            return False
    except:
        return False

def Stop():
    global stop
    stop = True

def full_scan(IP,PORT,KD,gov_num,T1_var,T2_var,T3_var,T4_var,T5_var,rss_ass_var,dead_var, ally_helps, ranged_points):
    try:
        global stop
        stop = False
        adb_path = 'adb\\adb.exe'
        adb.bridge = adb.enable_adb()
        question_seeding = am_i_working_seeding()
        question_ally_scan = Ally_script.am_i_working_ally_script()
        if question_seeding == True or question_ally_scan==True:
            tkinter.messagebox.showinfo(title="Error", message='You are already running a scan of the seed, wait to finish it before starting a new one')
        else:
            global im_working_all_info
            im_working_all_info = True

            IP_PORT = str(IP) + ':'+ str(PORT)
            connection = subprocess.run(f'{adb_path} connect {IP_PORT}', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
            if f'connected to {IP}:{PORT}' not in connection.stdout.decode('utf-8'):
                tkinter.messagebox.showinfo(title="Error",message=connection.stdout)
                return

            screen_size = subprocess.run(f'{adb_path} -s {IP}:{PORT} shell wm size', stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)
            # print(screen_size.stdout)
            if '1280x720' not in str(screen_size.stdout):
                print(screen_size.stdout)
                tkinter.messagebox.showinfo(title="Error",
                                            message='set your screen size to 1280x720, press the "?" to get a small tutorial')
                return

            st.inizialize(IP, PORT)
            st.start_scanning(IP, PORT)
            pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"
            datas = {
                'id': [],
                'alliance': [],
                'name': [],
                'real_name': [],
                'power': [],
                'kill_points': []
            }
            if T1_var == 1:
                datas['t1'] = []
            if T2_var == 1:
                datas['t2'] = []
            if T3_var == 1:
                datas['t3'] = []
            if T4_var == 1:
                datas['t4'] = []
            if T5_var == 1:
                datas['t5'] = []
            if dead_var == 1:
                datas['deaths'] = []
            if rss_ass_var == 1:
                datas['rss_assistance'] = []
            if ally_helps == 1:
                datas['ally_helps'] = []
            if ranged_points == 1:
                datas['ranged_points'] = []

            run = 1
            dynamic_y = 232

            while run <= int(gov_num):
                if stop == False:
                    if run <= 3:
                        time1 = time.time()
                        adb_path = 'adb\\adb.exe'
                        st.clear_logs(IP,PORT)
                        x, y = (633, dynamic_y)
                        st.tap(x, y, 1, IP,PORT)  # tapping the names on ranking list
                        sleep(0.5)
                        profile_image = st.Screenshot(IP, PORT)
                        sleep(0.5)
                        dynamic_y = dynamic_y + 80

                        #cv2.imwrite(f'chat.png', np.uint8(profile_image))

                        id = st.scan_id(profile_image, IP, PORT)
                        datas['id'].append(id)
                        name = st.scan_name(profile_image)
                        datas['name'].append(name)
                        power = st.scan_power(profile_image, IP,PORT)
                        datas['power'].append(power)
                        alliance = st.scan_alliance(profile_image)
                        datas['alliance'].append(alliance)
                        kill_points_info = st.Screenshot(IP,PORT)
                        kill_points = st.scan_kill_points(kill_points_info, IP, PORT)
                        datas['kill_points'].append(kill_points)
                        st.tap(895, 281, 1, IP,PORT)  # tapping on kill points info
                        sleep(1)
                        tier_screenshot = st.Screenshot(IP,PORT)
                        tier_kills = st.scan_tier_kills(tier_screenshot, IP, PORT)

                        if T1_var == 1:
                            try:
                                datas['t1'].append(tier_kills[0])
                            except:
                                datas['t1'].append('')
                        if T2_var == 1:
                            try:
                                datas['t2'].append(tier_kills[1])
                            except:
                                datas['t2'].append('')
                        if T3_var == 1:
                            try:
                                datas['t3'].append(tier_kills[2])
                            except:
                                datas['t3'].append('')
                        if T4_var == 1:
                            try:
                                datas['t4'].append(tier_kills[3])
                            except:
                                datas['t4'].append('')
                        if T5_var == 1:
                            try:
                                datas['t5'].append(tier_kills[4])
                            except:
                                datas['t5'].append('')
                        if ranged_points == 1:
                            try:
                                datas['ranged_points'].append(tier_kills[5])
                            except:
                                print('exception')
                                datas['ranged_points'].append('')

                        st.tap(326, 533, 1, IP,PORT)  # tapping on more_info
                        more_info_image = st.Screenshot(IP,PORT)

                        if ally_helps == 1:
                            ally_helps_number = st.scan_ally_helps(more_info_image)
                            datas['ally_helps'].append(ally_helps_number)
                        if dead_var == 1:
                            deaths = st.scan_deaths(more_info_image)
                            datas['deaths'].append(deaths)


                        if rss_ass_var == 1:
                            rss_assistance = st.scan_rss_assistance(more_info_image)
                            datas['rss_assistance'].append(rss_assistance)

                        real_name = st.scan_real_name(IP,PORT)
                        datas['real_name'].append(real_name)

                        st.clear_logs(IP,PORT)
                        st.tap(1115, 43, 1, IP,PORT)  # tapping on close more_info
                        st.tap(1092, 82, 1, IP,PORT)  # tapping on close profile window
                        time2 = time.time()
                        print(time2 - time1)
                        run = run + 1
                    else:
                        time1 = time.time()
                        adb_path = 'adb\\adb.exe'
                        subprocess.run(f'{adb_path} -s {IP_PORT} shell logcat -b all -c', stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE)

                        x, y = (633, 473)

                        st.tap(x, y, 1, IP,PORT)
                        sleep(0.5)
                        profile_image = st.Screenshot(IP, PORT)
                        ####TEST
                        testing = cv2.cvtColor(np.array(profile_image), cv2.COLOR_RGB2GRAY)
                        target = cv2.imread('images/chat.png', 0)
                        h, w = target.shape
                        result1 = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result1)
                        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                            location = min_loc
                        else:
                            location = max_loc
                        #
                        if max_val < 0.7:
                            subprocess.run(
                                f'{adb_path} -s {str(IP).strip()}:{PORT} shell input swipe 613 650 613 573 6000',
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
                            continue

                        sleep(0.5)

                        #profile_image = st.Screenshot(IP,PORT)
                        id = st.scan_id(profile_image, IP, PORT)
                        datas['id'].append(id)
                        name = st.scan_name(profile_image)
                        datas['name'].append(name)
                        power = st.scan_power(profile_image, IP,PORT)
                        datas['power'].append(power)
                        alliance = st.scan_alliance(profile_image)
                        datas['alliance'].append(alliance)
                        kill_points_info = st.Screenshot(IP,PORT)
                        kill_points = st.scan_kill_points(kill_points_info, IP, PORT)
                        datas['kill_points'].append(kill_points)
                        st.tap(895, 281, 1, IP,PORT)  # tapping on kill points info
                        sleep(1)
                        tier_screenshot = st.Screenshot(IP,PORT)
                        tier_kills = st.scan_tier_kills(tier_screenshot, IP, PORT)
                        if T1_var == 1:
                            try:
                                datas['t1'].append(tier_kills[0])
                            except:
                                datas['t1'].append('')
                        if T2_var == 1:
                            try:
                                datas['t2'].append(tier_kills[1])
                            except:
                                datas['t2'].append('')
                        if T3_var == 1:
                            try:
                                datas['t3'].append(tier_kills[2])
                            except:
                                datas['t3'].append('')
                        if T4_var == 1:
                            try:
                                datas['t4'].append(tier_kills[3])
                            except:
                                datas['t4'].append('')
                        if T5_var == 1:
                            try:
                                datas['t5'].append(tier_kills[4])
                            except:
                                datas['t5'].append('')
                        if ranged_points == 1:
                            try:
                                datas['ranged_points'].append(tier_kills[5])
                            except:
                                datas['ranged_points'].append('')

                        st.tap(326, 533, 1, IP,PORT)  # tapping on more_info
                        more_info_image = st.Screenshot(IP,PORT)

                        if ally_helps == 1:
                            ally_helps_number = st.scan_ally_helps(more_info_image)
                            datas['ally_helps'].append(ally_helps_number)

                        if dead_var == 1:
                            deaths = st.scan_deaths(more_info_image)
                            datas['deaths'].append(deaths)
                        if rss_ass_var == 1:
                            rss_assistance = st.scan_rss_assistance(more_info_image)
                            datas['rss_assistance'].append(rss_assistance)

                        real_name = st.scan_real_name(IP, PORT)
                        datas['real_name'].append(real_name)

                        st.clear_logs(IP,PORT)
                        st.tap(1115, 43, 1, IP,PORT)  # tapping on close more_info
                        st.tap(1092, 82, 1, IP,PORT)  # tapping on close profile window
                        time2 = time.time()
                        print(time2 - time1)
                        run = run + 1
                else:
                    im_working_all_info = False
                    return
            sheet_name = date.today()
            kingdom = KD
            try:
                path = f'datas/kd_datas/{kingdom}_details.xlsx'
                book = load_workbook(path)
                writer = pd.ExcelWriter(path, engine='openpyxl')
                writer.book = book
                df = pd.DataFrame(datas)
                df.to_excel(writer, sheet_name=f'{sheet_name}')
                writer.close()
            except:
                df = pd.DataFrame(datas)
                dfExcel = pd.ExcelWriter(f"datas\\kd_datas\\{kingdom}_details.xlsx", engine='xlsxwriter')
                df.to_excel(dfExcel, sheet_name=f'{sheet_name}')
                dfExcel.save()

            im_working_all_info = False
            excel_name = f'datas\\kd_datas\\{kingdom}_details.xlsx'
            return File_pops.OpenFile(excel_name)
    except Exception as e:
        tkinter.messagebox.showinfo(title="Error", message='Unluckly an error occured during the execution of the scan, please contact ROKStats')
        return print('An error occured during general info scan: ', e)