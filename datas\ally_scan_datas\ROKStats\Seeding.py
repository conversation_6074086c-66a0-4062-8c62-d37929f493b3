import pytesseract
from PIL import Image
import Ally_script
import adb
import tkinter
import subprocess
import tkinter.messagebox
import scanning_tasks as st
import pandas as pd
from datetime import date
from time import sleep
import cv2
import numpy
import all_info
import File_pops
from openpyxl import load_workbook
import numpy as np

def am_i_working_seeding():
    global im_working_seeding
    try:
        if im_working_seeding == True:
            return True
        else:
            return False
    except:
        return False

def Stop():
    global stop
    stop = True

def Seeding(IP, PORT, KD, gov_num,kraken_var):
    KD_list = KD.split(';')
    for kingdom in KD_list:
        if len(KD_list) > 1:
            st.inizialize(IP, PORT)
            feedback = st.check_profile(kingdom, IP,PORT)
            if feedback == True:
                sleep(15)
                seed_scan(IP, PORT, kingdom, gov_num,kraken_var)
        else:
            seed_scan(IP, PORT, kingdom, gov_num,kraken_var)





def seed_scan(IP, PORT, KD, gov_num,kraken_var):
    try:
        global stop
        stop = False
        adb_path = 'adb\\adb.exe'
        adb.bridge = adb.enable_adb()
        IP_PORT = str(IP) + ':'+ str(PORT)
        connection = subprocess.run(f'{adb_path} connect {IP_PORT}', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
        if f'connected to {IP}:{PORT}' not in connection.stdout.decode('utf-8'):
            tkinter.messagebox.showinfo(title="Error",message=connection.stdout)
            return
        screen_size = subprocess.run(f'{adb_path} -s {IP}:{PORT} shell wm size', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
        #print(screen_size.stdout)
        if '1280x720' not in str(screen_size.stdout):
            tkinter.messagebox.showinfo(title="Error", message='set your screen size to 1280x720, press the "?" to get a small tutorial')
            return
        question_all_info = all_info.am_i_working_all_info()
        question_ally_scan = Ally_script.am_i_working_ally_script()
        if question_all_info == True or question_ally_scan == True:
            tkinter.messagebox.showinfo(title="Error",
                                        message='You are already running a scan, wait to finish it before starting a new one or press STOP')
        else:
            global im_working_seeding
            im_working_seeding = True
            st.inizialize(IP, PORT)
            #st.check_profile(KD,IP,PORT)
            st.start_scanning(IP, PORT)
            pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"
            run = 0
            datas = {
                'rank':[],
                'power':[]
            }
            run = 1
            where_we_are = 1

            while run <= int(gov_num):
                if stop == False:
                    if run <= 6:
                        screenshot = st.Screenshot(IP,PORT)
                        sleep(2)
                        dynamic_y = 0
                        for x in range(1,7):
                            if where_we_are <= int(gov_num):
                                dynamic_box =(895, 202+dynamic_y, 1087, 250+dynamic_y)
                                #power_image = screenshot.crop(dynamic_box)
                                #img_gray = cv2.cvtColor(numpy.uint8(power_image), cv2.COLOR_BGR2GRAY)
                                #power = pytesseract.image_to_string(img_gray,config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                power = ''
                                while power == '':
                                    #print('needed')
                                    #screenshot = screenshot
                                    new_power_image = screenshot.crop(dynamic_box)
                                    cv2.imwrite(f'temp_seeding.png', numpy.uint8(new_power_image))
                                    re_open = cv2.imread(f'temp_seeding.png')
                                    (h, w) = re_open.shape[:2]
                                    re_open = cv2.resize(re_open, (w * 3, h * 3))
                                    power = pytesseract.image_to_string(re_open,
                                                                        config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')

                                print(int(power))
                                if int(power) > 150000000 and kraken_var ==1:
                                    power = 150000000

                                datas['rank'].append(where_we_are)
                                datas['power'].append(int(power))
                                dynamic_y += 80
                                where_we_are += 1
                        y = 630
                        x = 550
                        st.tap(550, 630, 1, IP, PORT) ##tapping on profile

                        profile_image = st.Screenshot(IP, PORT)

                        testing = cv2.cvtColor(np.array(profile_image), cv2.COLOR_RGB2GRAY)
                        target = cv2.imread('images/chat.png', 0)
                        h, w = target.shape
                        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                            location = min_loc
                        else:
                            location = max_loc

                        if max_val < 0.7:
                            sleep(1)
                            subprocess.run(
                                f'{adb_path} -s {str(IP).strip()}:{PORT} shell input swipe 613 650 613 420 6000',
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
                            run += 3
                            continue

                        sleep(1)
                        st.tap(1092, 82, 1, IP, PORT)
                        run += 6
                    else:
                        sleep(1)
                        screenshot = st.Screenshot(IP, PORT)
                        dynamic_y = 0
                        for x in range(1,4):
                            if where_we_are <= int(gov_num):
                                dynamic_box = (940, 466+dynamic_y, 1060, 502+dynamic_y)
                                #power_image = screenshot.crop(dynamic_box)
                                #img_gray = cv2.cvtColor(numpy.uint8(power_image), cv2.COLOR_BGR2GRAY)
                                #power = pytesseract.image_to_string(img_gray,config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                power = ''
                                while power == '':
                                    #print('needed')
                                    new_power_image = screenshot.crop(dynamic_box)
                                    cv2.imwrite(f'temp_seeding.png', numpy.uint8(new_power_image))
                                    re_open = cv2.imread(f'temp_seeding.png')
                                    (h, w) = re_open.shape[:2]
                                    #re_open = cv2.resize(re_open, (w * 3, h * 3))
                                    power = pytesseract.image_to_string(re_open,
                                                                        config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                print(int(power))
                                if int(power.replace(',','').replace('\n','').replace('.','')) > 150000000 and kraken_var ==1:
                                    power = 150000000

                                datas['rank'].append(where_we_are)
                                datas['power'].append(int(power.replace(',','').replace('\n','').replace('.','')))
                                dynamic_y += 80
                                where_we_are += 1
                        y = 630
                        x = 550
                        st.tap(550, 630, 1, IP, PORT)
                        sleep(0.5)

                        ####TEST
                        profile_image = st.Screenshot(IP, PORT)

                        testing = cv2.cvtColor(np.array(profile_image), cv2.COLOR_RGB2GRAY)
                        target = cv2.imread('images/chat.png', 0)
                        h, w = target.shape
                        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                            location = min_loc
                        else:
                            location = max_loc

                        if max_val < 0.7:
                            sleep(1)
                            subprocess.run(
                                f'{adb_path} -s {str(IP).strip()}:{PORT} shell input swipe 613 650 613 420 6000',
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
                            run +=3
                            continue
                        sleep(1)
                        st.tap(1092, 82, 1, IP, PORT)
                        sleep(0.5)
                        run += 3
                else:
                    im_working_seeding = False
                    return
            sheet_name = date.today()
            kingdom = KD
            datas['rank'].append('Seed Power')
            datas['power'].append(sum(datas['power']))
            try:
                path = f'datas/seed_datas/{kingdom}_seed.xlsx'
                book = load_workbook(path)
                writer = pd.ExcelWriter(path, engine='openpyxl')
                writer.book = book
                df = pd.DataFrame(datas)
                df.to_excel(writer, sheet_name=f'{sheet_name}')
                writer.close()
            except:
                df = pd.DataFrame(datas)
                dfExcel = pd.ExcelWriter(f"datas\\seed_datas\\{kingdom}_seed.xlsx", engine='xlsxwriter')
                df.to_excel(dfExcel, sheet_name=f'{sheet_name}')
                dfExcel.save()
            im_working_seeding = False
            excel_name = f'datas\\seed_datas\\{kingdom}_seed.xlsx'
            return File_pops.OpenFile(excel_name)

    except Exception as e:
        tkinter.messagebox.showinfo(title="Error",
                                    message='Unluckly an error occured during the execution of the scan, please contact ROKStats')
        return print('An error occured during seeding info scan:' , e)

