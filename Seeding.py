import pytesseract
from PIL import Image
import Ally_script
import adb
import tkinter
import subprocess
import tkinter.messagebox
from openpyxl.utils import get_column_letter
import scanning_tasks
import scanning_tasks as st
import pandas as pd
from datetime import date
from time import sleep
import cv2
import numpy
import all_info
import File_pops
from openpyxl import load_workbook
from openpyxl.styles import PatternFill, Font, Border, Side
import numpy as np
import time
import sys
from keyauth import api
import hashlib
from datetime import datetime, date
import json

def am_i_working_seeding():
    global im_working_seeding
    try:
        if im_working_seeding == True:
            return True
        else:
            return False
    except:
        return False

def Stop():
    global stop
    stop = True
def modify_image(re_open):
    img = cv2.resize(re_open, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
    # Convert the image to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Apply a threshold to the image to make the text more visible
    thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]
    # Perform some morphological operations to remove noise and smooth the image
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=1)
    dilate = cv2.dilate(opening, kernel, iterations=1)
    return dilate


def Seeding(IP, PORT, KD, gov_num,kraken_var, username, password):

    KD_list = KD.split(';')
    adb_path = 'adb\\adb.exe'
    subprocess.run([f'{adb_path}', '-P', '5038', 'start-server'])
    IP_PORT = str(IP) + ':' + str(PORT)
    #subprocess.run(['adb', '-P', '5038', 'connect', IP_PORT])
    #adb.bridge = adb.enable_adb()

    #subprocess.run(['adb', '-P', '5038', 'connect', IP_PORT])
    connection = subprocess.run(f'{adb_path} -P 5038 connect {IP_PORT}', stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    #connection = subprocess.run(f'adb -P 5038 connect {IP_PORT}', stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    d = json.load(open('config.json'))
    multiple_kd = d['multiple_kd']
    if f'connected to {IP}:{PORT}' not in connection.stdout.decode('utf-8'):
        tkinter.messagebox.showinfo(title="Error", message=connection.stdout)
        return
    counter = 0
    iteration = 1
    st.inizialize(IP,PORT)
    global variable_of_success
    variable_of_success = False
    global stop
    stop = False
    if multiple_kd and stop == False:
        sleep(2)
        n_scan = st.extract_n_scans(IP, PORT)
        try:
            n_scan = int(n_scan)
        except:
            n_scan = n_scan
        while iteration <= n_scan and stop == False:
            iteration +=1
            if counter == 0:
                counter+=1
                st.inizialize(IP, PORT)
                kingdom = st.check_profile(IP,PORT)
                seed_scan(IP, PORT, kingdom, gov_num,kraken_var, username, password)
            else:
                counter += 1
                #open profile
                st.inizialize(IP,PORT)

                # press profile pic
                st.tap(50, 45, 1, IP, PORT)
                sleep(1)
                # press settings
                st.tap(1008, 584, 1, IP, PORT)
                sleep(1)
                # press characters
                st.tap(346, 376, 1, IP, PORT)
                sleep(5)
                # check for the green flag
                x,y = st.check_flag(IP, PORT)
                if x < 630:
                    #do something
                    x = x+570
                    st.tap(x,y,1,IP,PORT)
                    sleep(1)
                    st.tap(811,514,1,IP,PORT)
                    sleep(15)
                    st.inizialize(IP, PORT)
                    kingdom = st.check_profile(IP, PORT)
                    seed_scan(IP, PORT, kingdom, gov_num, kraken_var, username, password)
                else:
                    if x >630 and y>471:
                        st.swipe_down(IP,PORT)

                        x,y = st.check_flag(IP,PORT)
                    #do something else
                    x = x-302
                    y = y + 150
                    st.tap(x,y,1,IP,PORT)
                    sleep(1)
                    st.tap(811, 514, 1, IP, PORT)
                    sleep(15)
                    st.inizialize(IP, PORT)
                    kingdom = st.check_profile(IP, PORT)
                    seed_scan(IP, PORT, kingdom, gov_num, kraken_var, username, password)

    else:
        seed_scan(IP, PORT, KD, gov_num,kraken_var, username, password)
    if variable_of_success == True:
        return 'Done'
    else:
        return





def seed_scan(IP, PORT, KD, gov_num,kraken_var, username, password):
    try:
        global variable_of_success
        variable_of_success = False
        global stop
        stop = False
        adb_path = 'adb\\adb.exe'

        subprocess.run([f'{adb_path}', '-P', '5038', 'start-server'])
        #adb.bridge = adb.enable_adb()
        IP_PORT = str(IP) + ':'+ str(PORT)
        connection = subprocess.run(f'{adb_path} -P 5038 connect {IP_PORT}', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
        if f'connected to {IP}:{PORT}' not in connection.stdout.decode('utf-8'):
            tkinter.messagebox.showinfo(title="Error",message=connection.stdout)
            return
        screen_size = subprocess.run(f'{adb_path} -P 5038 -s {IP}:{PORT} shell wm size', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
        #print(screen_size.stdout)
        if '1280x720' not in str(screen_size.stdout):
            tkinter.messagebox.showinfo(title="Error", message='set your screen size to 1280x720, press the "?" to get a small tutorial')
            return
        question_all_info = all_info.am_i_working_all_info()
        question_ally_scan = Ally_script.am_i_working_ally_script()
        if question_all_info == True or question_ally_scan == True:
            tkinter.messagebox.showinfo(title="Error",
                                        message='You are already running a scan, wait to finish it before starting a new one or press STOP')
        else:
            global im_working_seeding
            im_working_seeding = True
            st.inizialize(IP, PORT)
            #st.check_profile(KD,IP,PORT)
            st.start_scanning(IP, PORT)
            st.ranking_open(IP, PORT)
            pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"

            datas = {
                'rank':[],
                'power':[]
            }

            variable_of_success = False
            run = 1
            where_we_are = 1
            total_time = 0
            while run <= int(gov_num):
                if stop == False:
                    if run <= 6:
                        time1 = time.time()
                        sleep(2)
                        dynamic_y = 0
                        for x in range(1,7):
                            if where_we_are <= int(gov_num):
                                dynamic_box =(895, 202+dynamic_y, 1087, 250+dynamic_y)
                                #power_image = screenshot.crop(dynamic_box)
                                #img_gray = cv2.cvtColor(numpy.uint8(power_image), cv2.COLOR_BGR2GRAY)
                                #power = pytesseract.image_to_string(img_gray,config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                power = ''
                                while power == '':
                                    sleep(0.5)
                                    #print('needed')
                                    screenshot = st.Screenshot(IP, PORT)
                                    #screenshot = screenshot
                                    print(f'Trying to extract power of rank {where_we_are}')
                                    new_power_image = screenshot.crop(dynamic_box)
                                    cv2.imwrite(f'temp_seeding.png', numpy.uint8(new_power_image))
                                    re_open = cv2.imread(f'temp_seeding.png')
                                    dilate = modify_image(re_open)
                                    power = pytesseract.image_to_string(dilate,
                                                                        config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                    #print(power)

                                print(int(power))
                                if int(power) > 150000000 and kraken_var ==1:
                                    power = 150000000

                                datas['rank'].append(where_we_are)
                                datas['power'].append(int(power))
                                dynamic_y += 80
                                where_we_are += 1
                        y = 630
                        x = 550
                        st.tap(550, 630, 1, IP, PORT) ##tapping on profile
                        sleep(1)
                        profile_image = st.Screenshot(IP, PORT)

                        testing = cv2.cvtColor(np.array(profile_image), cv2.COLOR_RGB2GRAY)
                        target = cv2.imread('images/chat.png', 0)
                        h, w = target.shape
                        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                            location = min_loc
                        else:
                            location = max_loc

                        if max_val < 0.7:
                            print("Profile didn't open, skipping it")
                            sleep(1)
                            subprocess.run(
                                f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 613 650 613 420 6000',
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
                            run += 3
                            sleep(1)
                            continue

                        sleep(1)
                        st.tap(1092, 82, 1, IP, PORT)
                        time2 = time.time()
                        iteration_time = time2 - time1
                        total_time += iteration_time
                        average_time = total_time / (run)

                        remaining_iterations = int(gov_num) - (run)
                        remaining_time = (average_time * remaining_iterations) / 60
                        process_time = (average_time * int(gov_num)) / 60
                        print(
                            f"Iteration {run}/{gov_num} - Time: {iteration_time:.2f}s - Remaining time: {remaining_time:.2f} minutes - Estimated total time: {process_time:.2f} minutes")
                        run += 6
                    else:
                        time1 = time.time()
                        sleep(1)
                        #screenshot = st.Screenshot(IP, PORT)
                        dynamic_y = 0
                        for x in range(1,4):
                            if where_we_are <= int(gov_num):
                                dynamic_box = (940, 466+dynamic_y, 1060, 502+dynamic_y)
                                #power_image = screenshot.crop(dynamic_box)
                                #img_gray = cv2.cvtColor(numpy.uint8(power_image), cv2.COLOR_BGR2GRAY)
                                #power = pytesseract.image_to_string(img_gray,config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                power = ''
                                while power == '':
                                    #print('needed')
                                    screenshot = st.Screenshot(IP, PORT)
                                    print(f'Trying to extract power of rank {where_we_are}')
                                    new_power_image = screenshot.crop(dynamic_box)
                                    cv2.imwrite(f'temp_seeding.png', numpy.uint8(new_power_image))
                                    re_open = cv2.imread(f'temp_seeding.png')
                                    dilate = modify_image(re_open)
                                    power = pytesseract.image_to_string(dilate,
                                                                        config='--psm 10 --oem 3 -c tessedit_char_whitelist=0123456789')
                                print(int(power))
                                if int(power) > 150000000 and kraken_var ==1:
                                    power = 150000000

                                datas['rank'].append(where_we_are)
                                datas['power'].append(int(power))
                                dynamic_y += 80
                                where_we_are += 1
                        y = 630
                        x = 550
                        st.tap(550, 630, 1, IP, PORT)
                        sleep(1)

                        ####TEST
                        profile_image = st.Screenshot(IP, PORT)

                        testing = cv2.cvtColor(np.array(profile_image), cv2.COLOR_RGB2GRAY)
                        target = cv2.imread('images/chat.png', 0)
                        h, w = target.shape
                        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                            location = min_loc
                        else:
                            location = max_loc

                        if max_val < 0.7:
                            print("Profile didn't open, skipping it")
                            sleep(1)
                            subprocess.run(
                                f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input swipe 613 650 613 420 6000',
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE)
                            run +=3
                            sleep(1)
                            continue
                        sleep(1)
                        st.tap(1092, 82, 1, IP, PORT)
                        sleep(0.5)
                        time2 = time.time()
                        iteration_time = time2 - time1
                        total_time += iteration_time
                        average_time = total_time / (run)

                        remaining_iterations = int(gov_num) - (run)
                        remaining_time = (average_time * remaining_iterations) / 60
                        process_time = (average_time * int(gov_num)) / 60
                        print(
                            f"Iteration {run}/{gov_num} - Time: {iteration_time:.2f}s - Remaining time: {remaining_time:.2f} minutes - Estimated total time: {process_time:.2f} minutes")
                        run += 3
                    variable_of_success = True


                else:
                    variable_of_success = False
                    im_working_seeding = False
                    return

            sheet_name = date.today()
            kingdom = int(KD)
            #datas['rank'].append('Seed Power')
            #datas['power'].append(sum(datas['power']))

            df = pd.DataFrame(datas)
            corrected_powers = scanning_tasks.check_power_values(df)
            #print(corrected_powers)
            df['power'] = corrected_powers  # Update the 'power' column with corrected values
            # Calculate the sum of the 'power' column
            total_power = df['power'].sum()
            # Append the total_power value to a new row in the 'power' column
            df.loc[len(df.index), 'power'] = total_power
            # Format the 'Power' column values with commas as thousands separators
            df['power'] = df['power'].apply(lambda x: '{:,.0f}'.format(x))

            # Add "Seed Power" to the 'Rank' column where total_power is located
            df.loc[len(df.index) - 1, 'rank'] = 'Seed Power'
            try:
                path = f'datas/seed_datas/{kingdom}_seed.xlsx'
                book = load_workbook(path)
                writer = pd.ExcelWriter(path, engine='openpyxl')
                writer.book = book

                df.to_excel(writer, sheet_name=f'{sheet_name}')
                writer.close()


            except:
                #df = pd.DataFrame(datas)
                dfExcel = pd.ExcelWriter(f"datas\\seed_datas\\{kingdom}_seed.xlsx", engine='xlsxwriter')
                df.to_excel(dfExcel, sheet_name=f'{sheet_name}')
                dfExcel.close()

                wb = load_workbook(f"datas\\seed_datas\\{kingdom}_seed.xlsx")
                # Select the active worksheet
                ws = wb.active
                # Apply color to the cell with 'Seed Power'
                seed_power_cell = ws.cell(row=len(df.index)+1, column=2)  # Assuming 'Seed Power' is in the Rank column
                seed_power_cell_value = ws.cell(row=len(df.index) + 1,
                                          column=3)  # Assuming value of sede power
                yellow_fill = PatternFill(start_color='949090', end_color='949090', fill_type='solid')
                seed_power_cell.fill = yellow_fill

                # Apply bold font
                bold_font = Font(bold=True)
                seed_power_cell.font = bold_font
                seed_power_cell_value.font = bold_font
                # Apply border around the cell
                border = Border(left=Side(border_style='thin', color='000000'),
                                right=Side(border_style='thin', color='000000'),
                                top=Side(border_style='thin', color='000000'),
                                bottom=Side(border_style='thin', color='000000'))
                seed_power_cell.border = border
                seed_power_cell_value.border = border
                # Set column width based on content
                for column in ws.columns:
                    max_length = 0
                    column = [cell for cell in column]
                    for cell in column:
                        try:
                            #print(cell.value)
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except Exception as e:
                            #print(e)
                            pass
                    adjusted_width = (max_length + 2)  # Add padding
                    ws.column_dimensions[get_column_letter(column[0].column)].width = adjusted_width
                wb.save(f"datas\\seed_datas\\{kingdom}_seed.xlsx")
            excel_name = f'datas\\seed_datas\\{kingdom}_seed.xlsx'
            File_pops.OpenFile(excel_name)
            im_working_seeding = False
            return

    except Exception as e:
        tkinter.messagebox.showinfo(title="Error",
                                    message='Unluckly an error occured during the execution of the scan, please contact ROKStats')
        return print('An error occured during seeding info scan:' , e)

