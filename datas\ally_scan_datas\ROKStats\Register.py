import tkinter as tk
from tkinter import *
from tkinter import ttk
import json
from datetime import datetime
from keyauth import api
import sys
#import platform
import os
import hashlib
from GUI_ROKSCAN import ROKSCAN
#, KeyPage, RegisterPage, ContactPage
from PIL import ImageTk
from KeyPage import KeyPage
from Contact import ContactPage
import PIL.Image
import tkinter.messagebox
import GUI_LoginPage as GLP


class RegisterPage:
    def __init__(self,window):
        def BackToLogin_event():
            GLP.LoginPage(window)
        def Register_Event():
            username = self.username_entry.get()
            password = self.password_entry.get()
            key = self.key_entry.get()

            def getchecksum():
                md5_hash = hashlib.md5()
                file = open(''.join(sys.argv), "rb")
                md5_hash.update(file.read())
                digest = md5_hash.hexdigest()
                return digest

            keyauthapp = api(
                name="ROKScan",
                ownerid="Ykbj23aFHl",
                secret="e0485dc3f4740a3f9d9de4c95feee7953db5da95f3c10d83c490b8fe56666900",
                version="1.0",
                hash_to_check=getchecksum()
            )
            msg = keyauthapp.register(username, password, key)
            if msg == "successfully registered":

                try:
                    expire = datetime.utcfromtimestamp(int(keyauthapp.user_data.expires))
                except:
                    expire = 'lifetime license'
                ROKSCAN(window,expire)
            elif msg == 'Username Already Exists.':
                self.used_label = Label(self.lgn_frame, text="Username already used, please choose a different one", bg="#040405",
                                        fg="#dc143c",
                                        font=("yu gothic ui", 12, "bold"))
                self.used_label.place(x=50, y=340)
                self.used_label.after(3000, lambda: self.used_label.destroy())
            elif msg == 'Key Not Found.':
                self.used_label = Label(self.lgn_frame, text="Insert a valid Key",
                                        bg="#040405",
                                        fg="#dc143c",
                                        font=("yu gothic ui", 12, "bold"))
                self.used_label.place(x=180, y=340)
                self.used_label.after(3000, lambda: self.used_label.destroy())
            else:
                print(msg)
        def contact_event():
            ContactPage(window)
        self.window = window
        self.window.geometry('800x700')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('Register Page')
        self.bg_frame = PIL.Image.open('images\\background1.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        self.lgn_frame = Frame(self.window, bg='#040405', width=500, height=500)
        self.lgn_frame.place(x=150, y=70)
        self.get_key_button = Button(self.lgn_frame, text="press to get a free trial key",
                                    font=("yu gothic ui", 10, "bold underline"), fg="white", relief=FLAT,
                                    activebackground="#040405"
                                    , borderwidth=0, background="#040405", cursor="hand2")
        self.get_key_button.place(x=140, y=450)

        ##CONTACT BUTTON
        self.cnt_button = PIL.Image.open('images\\contact.png')
        cnt_photo = ImageTk.PhotoImage(self.cnt_button.resize((230,60)))
        self.cnt_button_label = Label(self.lgn_frame, image=cnt_photo, bg='#040405')
        self.cnt_button_label.image = cnt_photo
        self.cnt_button_label.place(x=115, y=360)

        self.contact = Button(self.cnt_button_label, text='CONTACT', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#7f7f7f', cursor='hand2', activebackground='#7f7f7f', fg='#bdb9b1', command= contact_event)
        self.contact.place(x=55, y=12)


        self.txt = "ROKSTATS"
        self.heading = Label(self.lgn_frame, text=self.txt, font=('yu gothic ui', 25, "bold"), bg="#040405",
                             fg='white',
                             bd=5,
                             relief=FLAT)
        self.heading.place(x=100, y=10, width=300, height=30)

        self.sign_in_image = PIL.Image.open('images\\hyy.png')
        photo = ImageTk.PhotoImage(self.sign_in_image.resize((40,30)))

        self.sign_in_image_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.sign_in_image_label.image = photo
        self.sign_in_image_label.place(x=50, y=130)

        self.sign_in_label = Label(self.lgn_frame, text="Register", bg="#040405", fg="white",
                                    font=("yu gothic ui", 18, "bold"))
        self.sign_in_label.place(x=100, y=70)

        self.username_label = Label(self.lgn_frame, text="Username", bg="#040405", fg="#4f4e4d",
                                    font=("yu gothic ui", 13, "bold"))
        self.username_label.place(x=100, y=105)

        self.username_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui ", 12, "bold"),insertbackground="white")
        self.username_entry.place(x=100, y=130, width=270)

        self.username_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.username_line.place(x=100, y=160)

        ##REGISTER
        self.start_button = PIL.Image.open('images\\btn1.png')
        start_photo = ImageTk.PhotoImage(self.start_button.resize((230,60)))
        self.start_button_label = Label(self.lgn_frame, image=start_photo, bg='#040405')
        self.start_button_label.image = start_photo
        self.start_button_label.place(x=115, y=280)

        self.start = Button(self.start_button_label, text='REGISTER', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#c3c3c3', cursor='hand2', activebackground='#c3c3c3', fg='black', command = Register_Event)
        self.start.place(x=55, y=12)



        self.log_in_button = Button(self.lgn_frame, text="Back to login",
                                    font=("yu gothic ui", 10, "bold underline"), fg="white", relief=FLAT,
                                    activebackground="#040405"
                                    , borderwidth=0, background="#040405", cursor="hand2",command=BackToLogin_event)
        self.log_in_button.place(x=215, y=50)

        self.password_label = Label(self.lgn_frame, text="Password", bg="#040405", fg="#4f4e4d",
                                    font=("yu gothic ui", 12, "bold"))
        self.password_label.place(x=100, y=165)

        self.password_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui", 12, "bold"), show="*",insertbackground="white")
        self.password_entry.place(x=100, y=190, width=244)

        self.password_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.password_line.place(x=100, y=210)

        #KEY START
        self.key_label = Label(self.lgn_frame, text="Key", bg="#040405", fg="#4f4e4d",
                                    font=("yu gothic ui", 12, "bold"))
        self.key_label.place(x=100, y=225)

        self.key_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui", 12, "bold"),insertbackground="white")
        self.key_entry.place(x=100, y=250, width=244)

        self.key_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.key_line.place(x=100, y=270)

        self.sign_in_image = PIL.Image.open('images\\input-key.png')
        photo = ImageTk.PhotoImage(self.sign_in_image.resize((36, 30)))

        self.sign_in_image_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.sign_in_image_label.image = photo
        self.sign_in_image_label.place(x=50, y=240)

        #KEY FINISH

        self.password_icon = PIL.Image.open('images\\password_icon.png')
        photo = ImageTk.PhotoImage(self.password_icon.resize((30,25)))
        self.password_icon_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.password_icon_label.image = photo
        self.password_icon_label.place(x=55, y=185)