import pytesseract
import adb
import tkinter
import subprocess
import tkinter.messagebox
import scanning_tasks
import scanning_tasks as st
import time
import pandas as pd
from datetime import datetime
from datetime import date
from time import sleep

import File_pops
import Ally<PERSON>script
from openpyxl import load_workbook
import cv2
import numpy as np
from openpyxl.utils import get_column_letter
adb_path = 'adb\\adb.exe'

def CheckForForts(ip,port):

    screenshot = st.Screenshot(ip,port)
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/Fort_nolvl.png', 0)
    cv2.imshow('test',target)
    h, w = target.shape
    result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.8:
        print("location:", location)
        print("max_val", max_val)
        st.tap(location[0], location[1], 1, ip, port)
        Start_Rally(ip,port)
        path_completed = True
        return path_completed

def Start_Rally(ip,port):
    screenshot = st.Screenshot(ip,port)
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/Rally_Button.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.8:
        print("location:", location)
        print("max_val", max_val)
        st.tap(location[0], location[1], 1, ip, port)
        sleep(0.5)
        st.tap(930, 300,1,ip,port)
        sleep(0.5)
        st.tap(1100, 288, 1, ip, port)
        sleep(0.5)
        st.tap(930, 630, 1,ip,port)
        sleep(0.5)
        screenshot = st.Screenshot(ip, port)
        profile_screenshot = screenshot.crop((29, 25, 65, 40))
        ##Join fort
        JoinFort(profile_screenshot,ip,'5875')

def FindFort(ip,port):
    max_steps = 10
    x, y = 600, 250  # Starting point coordinates
    steps = 0
    directions = 20
    direction = 1  # 1: down, 2: left, 3: up, 4: right
    count = 1  # Number of steps to take in the current direction
    while steps < max_steps:
        if direction % 2 == 1:  # Down
            for _ in range(count):
                subprocess.run(f"adb -P 5038 -s {ip}:{port} shell input swipe {x} {y} {x} {y + 500} 3000", shell=True)
                steps += 1
                if steps >= max_steps:
                    break
            direction = 2
        elif direction == 2:  # Left
            for _ in range(count):
                subprocess.run(f"adb -P 5038 -s {ip}:{port} shell input swipe {x} {y} {x - 500} {y} 3000", shell=True)
                steps += 1
                if steps >= max_steps:
                    break
            direction = 3
        elif direction == 3:  # Up
            for _ in range(count):
                subprocess.run(f"adb -P 5038 -s {ip}:{port} shell input swipe {x} {y} {x} {y - 500} 3000", shell=True)
                steps += 1
                if steps >= max_steps:
                    break
            direction = 4
        elif direction == 4:  # Right
            for _ in range(count):
                subprocess.run(f"adb -P 5038 -s {ip}:{port} shell input swipe {x} {y} {x + 500} {y} 3000", shell=True)
                steps += 1
                if steps >= max_steps:
                    break
            direction = 1
            count += 1  # Increase the number of steps for the next round
        # Check for cities
        path_completed = CheckForForts(ip, port)

# Launch Rally
def LaunchRally():
    ## Inizialize
    ip = '127.0.0.1'
    port = '5865'
    subprocess.run(f'{adb_path} -P 5038 connect {ip}:{port}', stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    st.inizialize(ip,port)
    ## Check for forts

    ###Go out of city
    st.tap(60,652,1,ip, port)
    sleep(0.5)
    #pinch_screen(ip,port)
    FindFort(ip,port)



def rally_lead_profile(profile_screenshot, ip, port):
    screenshot = cv2.cvtColor(np.array(st.Screenshot(ip,port)), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/profile_image.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.2:
        print("location:", location)
        print("max_val", max_val)
        return location


def JoinFort(profile_screenshot, ip,port):
    subprocess.run(f'{adb_path} -P 5038 connect {ip}:{port}', stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    #open rally panel
    st.tap(1030,666,1,ip, port)
    sleep(0.5)
    st.tap(620,404,1,ip,port)
    sleep(0.5)

    #check_rally_lead_profile
    location = rally_lead_profile(profile_screenshot, ip,port)
    st.tap(location[0], location[1], 1, ip, port)
    sleep(0.5)
    st.tap(440,440,1,ip,port)
    sleep(0.5)
    st.tap(1000, 130, 1, ip, port)
    sleep(0.5)
    st.tap(930, 630, 1, ip, port)
    sleep(0.5)


LaunchRally()