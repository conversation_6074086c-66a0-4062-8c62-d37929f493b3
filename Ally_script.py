import pytesseract
import adb
import tkinter
import subprocess
import tkinter.messagebox
import scanning_tasks as st
import time
import pandas as pd
from datetime import datetime
from datetime import date
from time import sleep
import cv2
import numpy
import all_info
#.am_i_working_all_info
import File_pops
import numpy as np
from openpyxl import load_workbook



def Stop():
    global stop
    stop = True

def am_i_working_ally_script():
    global im_working_ally_script
    try:
        if im_working_ally_script == True:
            return True
        else:
            return False
    except:
        return False

def ally(IP, PORT, KD, ally_numb):
    try:
        global stop
        stop = False
        adb_path = 'adb\\adb.exe'
        adb.bridge = adb.enable_adb()


        IP_PORT = str(IP) + ':'+ str(PORT)
        connection = subprocess.run(f'{adb_path} connect {IP_PORT}', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
        if f'connected to {IP}:{PORT}' not in connection.stdout.decode('utf-8'):
            tkinter.messagebox.showinfo(title="Error",message=connection.stdout)
            return
        screen_size = subprocess.run(f'{adb_path} -s {IP}:{PORT} shell wm size', stdout=subprocess.PIPE,stderr=subprocess.PIPE)
        #print(screen_size.stdout)
        if '1280x720' not in str(screen_size.stdout):
            tkinter.messagebox.showinfo(title="Error", message='set your screen size to 1280x720, press the "?" to get a small tutorial')
            return

        question_all_info = all_info.am_i_working_all_info()
        question_seeding = all_info.am_i_working_seeding()
        if question_all_info == True or question_seeding == True:
            tkinter.messagebox.showinfo(title="Error",
                                        message='You are already running a scan, wait to finish it before starting a new one')
        else:
            global im_working_ally_script
            im_working_ally_script = True
            st.inizialize(IP, PORT)

            pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"

            datas = {
                'ally_name':[],
                'id_leader':[],
                'power':[],
                'r4':[],
                'rss_gathered':[]
            }


            st.tap(50, 38, 1, IP, PORT)  # profile tap
            st.tap(397, 597, 1, IP,PORT)  # rankings tap
            st.tap(332, 193, 1, IP, PORT)  # rankings tap

            run = 1
            dynamic_y = 232

            while run <= int(ally_numb):
                if stop == False:
                    if run <= 3:
                        try:
                            x, y = (633, dynamic_y)
                            st.tap(x, y, 1, IP,PORT)
                            dynamic_y = dynamic_y + 80
                            screenshot = st.Screenshot(IP, PORT)
                            ally_name_box = (202,441 ,511, 476)
                            ally_name_image = screenshot.crop(ally_name_box)
                            ally_name = pytesseract.image_to_string(ally_name_image)
                            st.tap(1000,574,1,IP,PORT)
                            ally_screenshot = st.Screenshot(IP,PORT)
                            r4_box = (1018,240,1080,275)
                            r4_image = ally_screenshot.crop(r4_box)
                            r4 = pytesseract.image_to_string(r4_image)
                            datas['r4'].append(r4.strip())
                            sleep(1)
                            st.tap(437,144,1, IP, PORT)
                            sleep(1)
                            st.tap(574, 117,1, IP,PORT)
                            profile_image = st.Screenshot(IP,PORT)
                            id = st.scan_id(profile_image, IP, PORT)
                            datas['id_leader'].append(id)
                            if ally_name.strip() == '':
                                ally_name = f'couldnt scan ally name rank {run}'
                            datas['ally_name'].append(ally_name)
                            power = st.scan_power(profile_image,IP,PORT)
                            datas['power'].append(power)
                            st.tap(326, 533, 1, IP, PORT)
                            more_info_image = st.Screenshot(IP,PORT)
                            rss_gathered_box = (887,492,1056,523)
                            rss_gathered_image = more_info_image.crop(rss_gathered_box)
                            rss_gathered = pytesseract.image_to_string(rss_gathered_image)
                            datas['rss_gathered'].append(rss_gathered.replace(',',''))
                            st.tap(1115,42, 1, IP, PORT)
                            st.tap(1091, 84, 1, IP, PORT)
                            st.tap(1115, 45, 1, IP, PORT)
                            st.tap(1115, 42, 1, IP, PORT)
                            sleep(1)
                            run += 1
                        except:
                            st.tap(1115, 45, 1, IP, PORT)
                            st.tap(1115, 42, 1, IP, PORT)
                            datas['rss_gathered'].append('')
                            datas['power'].append('')
                            datas['ally_name'].append('')
                            datas['id_leader'].append('')
                            datas['r4'].append('')
                    else:
                        try:
                            x, y = (633, 473)
                            st.tap(x, y, 1, IP,PORT)
                            screenshot = st.Screenshot(IP, PORT)
                            ally_name_box = (202,441 ,511, 476)
                            ally_name_image = screenshot.crop(ally_name_box)
                            ally_name = pytesseract.image_to_string(ally_name_image)
                            st.tap(1000, 574, 1, IP, PORT)
                            ally_screenshot = st.Screenshot(IP, PORT)
                            r4_box = (1018, 240, 1080, 275)
                            r4_image = ally_screenshot.crop(r4_box)
                            r4 = pytesseract.image_to_string(r4_image)
                            datas['r4'].append(r4.strip())
                            sleep(1)
                            st.tap(437, 144, 1, IP, PORT)
                            sleep(1)
                            st.tap(574, 117,1, IP,PORT)
                            profile_image = st.Screenshot(IP, PORT)
                            id = st.scan_id(profile_image,IP, PORT)
                            datas['id_leader'].append(id)
                            power = st.scan_power(profile_image, IP, PORT)
                            datas['power'].append(power)
                            if ally_name.strip() == '':
                                ally_name = f'couldnt scan ally name rank {run}'
                            datas['ally_name'].append(ally_name)
                            st.tap(326, 533, 1, IP, PORT)
                            more_info_image = st.Screenshot(IP, PORT)
                            rss_gathered_box = (887, 492, 1056, 523)
                            rss_gathered_image = more_info_image.crop(rss_gathered_box)
                            rss_gathered = pytesseract.image_to_string(rss_gathered_image)
                            datas['rss_gathered'].append(rss_gathered.replace(',', ''))
                            st.tap(1115, 42, 1, IP, PORT)
                            st.tap(1091, 84, 1, IP, PORT)
                            st.tap(1115, 45, 1, IP, PORT)
                            st.tap(1115, 42, 1, IP, PORT)
                            sleep(1)
                            run += 1
                        except:
                            st.tap(1115, 45, 1, IP, PORT)
                            st.tap(1115, 42, 1, IP, PORT)
                            datas['rss_gathered'].append('')
                            datas['power'].append('')
                            datas['ally_name'].append('')
                            datas['id_leader'].append('')
                            datas['r4'].append('')
                else:
                    im_working_ally_script = False
                    return

            sheet_name = date.today()
            kingdom = KD
            try:
                path = f'datas/ally_scan_datas/{kingdom}_ally_scan.xlsx'
                book = load_workbook(path)
                writer = pd.ExcelWriter(path, engine='openpyxl')
                writer.book = book
                df = pd.DataFrame(datas)
                df.to_excel(writer, sheet_name=f'{sheet_name}')
                writer.close()
            except:
                df = pd.DataFrame(datas)
                dfExcel = pd.ExcelWriter(f"datas\\ally_scan_datas\\{kingdom}_ally_scan.xlsx", engine='xlsxwriter')
                df.to_excel(dfExcel, sheet_name=f'{sheet_name}')
                dfExcel.save()

            im_working_ally_script = False
            excel_name = f'datas\\ally_scan_datas\\{kingdom}_ally_scan.xlsx'
            return File_pops.OpenFile(excel_name)

    except Exception as e:
        tkinter.messagebox.showinfo(title="Error",
                            message='Unluckly an error occured during the execution of the scan, please contact ROKStats')
        return print('An error occured during general info scan: ', e)


