import pandas as pd
from tkinter import *
import os

path1 = r"D:\Program Files\ROKStats_\ROKStats\datas\kd_datas\2122_01-02-2023.xlsx"
path2 = r"D:\Program Files\ROKStats_\ROKStats\datas\kd_datas\2122_01-03-2023.xlsx"


#global path1, path2
df1 = pd.read_excel(path1)
df2 = pd.read_excel(path2)

file_name = os.path.basename(path2).replace('.xlsx','')
#print(t1_points,t2_points,t3_points,t4_points,t5_points,deads,rss_assistance,honor_points,ally_helps)

points_dict = {
    't1': 1,
    't2': 1,
    't3': 1,
    't4': 1,
    't5': 1,
    'deaths': 1,
    'rss_assistance': 1,
    'ranged_points': 1,
    'ally_helps': 1
}

# Drop unnecessary columns
df1 = df1.drop('Unnamed: 0',axis=1)
df2 = df2.drop('Unnamed: 0',axis=1)
tier_list = ['t1','t2','t3','t4','t5','deaths','rss_assistance','ally_helps']

df_merge = pd.merge(df1, df2, how="outer",on="id",suffixes=('_x', '_y'))

earned_points = []

for tier in tier_list:
# If 't1' column exists, create 't1_y' and 't1_x' columns and drop the original 't1' column
    if tier in df_merge.columns:
        df_merge[f'{tier}_y'] = df_merge[f'{tier}']
        df_merge[f'{tier}_x'] = df_merge[f'{tier}']
        df_merge = df_merge.drop(columns=[f'{tier}'])

    # If both 't1_y' and 't1_x' columns exist, calculate their difference and add it as a new column
    if f'{tier}_y' in df_merge.columns and f'{tier}_x' in df_merge.columns:
        if f'{tier}_diff' not in df_merge.columns:
            df_merge[f'{tier}_diff'] = (df_merge[f'{tier}_y']-df_merge[f'{tier}_x'])
        if f'{tier}_diff' in df_merge.columns:
            df_merge[f'{tier}_diff'] = (df_merge[f'{tier}_y']-df_merge[f'{tier}_x']) + df_merge[f'{tier}_diff']

        df_merge[f'{tier}_point'] = points_dict[f'{tier}']*(df_merge[f'{tier}_y']-df_merge[f'{tier}_x'])
        earned_points.append(f'{tier}_point')
    if f'{tier}_diff_y' in df_merge.columns and f'{tier}_diff_x' in df_merge.columns:
        df_merge[f'{tier}_diff'] = (df_merge[f'{tier}_y'] + df_merge[f'{tier}_x'])
        df_merge = df_merge.drop(columns=[f'{tier}_y'])
        df_merge = df_merge.drop(columns=[f'{tier}_x'])

if 'KVK_points' in df_merge.columns:
  df_merge['KVK_points'] =df_merge['KVK_points'] + df_merge[earned_points].sum(axis=1)
else:
    df_merge['KVK_points'] = df_merge[earned_points].sum(axis=1)

duplicated_cols2 = [col_name for col_name in df_merge.columns if col_name.endswith('_point')]
df_merge.drop(duplicated_cols2, axis=1, inplace=True)

# Get the list of duplicated columns
duplicated_cols = [col_name for col_name in df_merge.columns if col_name.endswith('_x')]

# Drop the duplicated columns
df_merge.drop(duplicated_cols, axis=1, inplace=True)
df_merge.columns = df_merge.columns.str.replace('_y', '')

df_merge = df_merge.sort_values(by=['power'], ascending=False )

df = pd.DataFrame(df_merge)
dfExcel = pd.ExcelWriter(f"datas\\kvk_datas\\{file_name}_kvk.xlsx", engine='xlsxwriter')
df.to_excel(dfExcel, engine='xlsxwriter')
dfExcel.close()











#for y in column:
#
#    try:
#
#        df_merge[f'{y}_diff'] = df_merge[f'{y}_y'] - df_merge[f'{y}_x']
#    except:
#        print('e')
#        pass
#    del df_merge[f'{y}_y']
#    del df_merge[f'{y}_x']
#
#df_merge['kvk_points'] = 0
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points'] + df_merge['t1_diff']*int(t1_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t2_diff']*int(t2_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t3_diff']*int(t3_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t3_diff']*int(t3_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t4_diff']*int(t4_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t5_diff']*int(t5_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['deaths_diff']*int(deads)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['rss_assistance_diff']*int(rss_assistance)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['rss_assistance_diff']*int(rss_assistance)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['honor_points_diff']*int(honor_points)
#except:
#    pass
#try:
#    df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['ally_helps_diff']*int(ally_helps)
#except:
#    pass
#sheet_name = date.today()
#
#for i in ignore[1:]:
#    del df_merge[f'{i}_x']
#del df_merge['Unnamed: 0_diff']
#
#df = pd.DataFrame(df_merge)
#dfExcel = pd.ExcelWriter(f"datas\\kvk_datas\\kvk_datas.xlsx", engine='xlsxwriter')
#df.to_excel(dfExcel, sheet_name=f'{sheet_name}')
#dfExcel.save()
#print(df_merge)
#
#kvk_merging()
