import tkinter as tk
from tkinter import *
from tkinter import ttk
import json
from datetime import datetime
from keyauth import api
import sys
import os
import hashlib
from Contact import ContactPage
from PIL import ImageTk
import PIL.Image
import threading
from all_info import full_scan
import GUI_ROKSCAN
import Seeding
from time import sleep
import ally_scan_page
import SeedScanPage
from tkinter import filedialog
from pathlib import Path
import pandas as pd
from datetime import date
from openpyxl import load_workbook
import webbrowser

class kvk_datas:
    def __init__(self, window,expire):


        def contact_event():
            ContactPage(window)

        def ROKSCAN_event():
            GUI_ROKSCAN.ROKSCAN(window,expire)


        def ally_scan_event():
            ally_scan_page.AllyScan(window,expire)

        def seed_scan_event():
            SeedScanPage.SeedScan(window,expire)

        def open_tutorial(e):
            window = tk.Toplevel()
            self.window = window
            # self.window.state('zoomed')
            self.window.title('Open')
            self.bg_frame = PIL.Image.open('images\\bluestacks_tutorial.png')
            photo = ImageTk.PhotoImage(self.bg_frame)
            self.bg_panel = Label(self.window, image=photo)
            self.bg_panel.image = photo
            self.bg_panel.pack(fill='both', expand='yes')

        def path1():
            global path1
            path1 = filedialog.askopenfilename()
            name = path1.split('/')[-1]

        def path2():
            global path2
            path2 = filedialog.askopenfilename()


        def open():
            global path1, path2
            df1 = pd.read_excel(path1)
            df2 = pd.read_excel(path2)

            t1_points = self.entry_1.get()
            t2_points = self.entry_2.get()
            t3_points = self.entry_3.get()
            t4_points = self.entry_4.get()
            t5_points = self.entry_5.get()
            deads = self.entry_6.get()
            rss_assistance = self.entry_7.get()
            honor_points = self.entry_8.get()
            ally_helps = self.entry_9.get()
            print(t1_points,t2_points,t3_points,t4_points,t5_points,deads,rss_assistance,honor_points,ally_helps)
            points_list = [t1_points,t2_points,t3_points,t4_points,t5_points,deads,rss_assistance,honor_points,ally_helps]
            column = []
            ignore = ['id','alliance','name','real_name','power', 'kill_points' ]

            for i in df1:
                if i not in ignore:
                    column.append(i)
            #kvk_datas_open.test()
            df_merge = pd.merge(df1, df2, how="inner", on="id")

            for y in column:

                try:

                    df_merge[f'{y}_diff'] = df_merge[f'{y}_y'] - df_merge[f'{y}_x']
                except:
                    print('e')
                    pass
                del df_merge[f'{y}_y']
                del df_merge[f'{y}_x']

            df_merge['kvk_points'] = 0
            try:
                df_merge['kvk_points'] = df_merge['kvk_points'] + df_merge['t1_diff']*int(t1_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t2_diff']*int(t2_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t3_diff']*int(t3_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t3_diff']*int(t3_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t4_diff']*int(t4_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['t5_diff']*int(t5_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['deaths_diff']*int(deads)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['rss_assistance_diff']*int(rss_assistance)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['rss_assistance_diff']*int(rss_assistance)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['honor_points_diff']*int(honor_points)
            except:
                pass
            try:
                df_merge['kvk_points'] = df_merge['kvk_points']+df_merge['ally_helps_diff']*int(ally_helps)
            except:
                pass
            sheet_name = date.today()

            for i in ignore[1:]:
                del df_merge[f'{i}_x']
            del df_merge['Unnamed: 0_diff']

            df = pd.DataFrame(df_merge)
            dfExcel = pd.ExcelWriter(f"datas\\kvk_datas\\kvk_datas.xlsx", engine='xlsxwriter')
            df.to_excel(dfExcel, sheet_name=f'{sheet_name}')
            dfExcel.save()
            print(df_merge)




        self.window = window
        self.window.geometry('800x700')
        self.window.resizable(0, 0)
        self.window.title('ROKStats - App')

        running = False

        self.bg_frame = PIL.Image.open('images\\background1.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        self.lgn_frame = Frame(self.window, bg='#040405', width=650, height=600)
        self.lgn_frame.place(x=80, y=60)



        self.txt = "ROKSTATS"
        self.heading = Label(self.lgn_frame, text=self.txt, font=('yu gothic ui', 25, "bold"), bg="#040405",
                             fg='white',
                             bd=5,
                             relief=FLAT)
        self.heading.place(x=180, y=10, width=300, height=30)




        ###MENU CANVAS LINES

        self.menu_line = Canvas(self.lgn_frame, width=606, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=75)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=150, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=300, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=452, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=25, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=604, y=50)

        self.menu_line = Canvas(self.lgn_frame, width=2, height=750, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=50)


        self.menu_line = Canvas(self.lgn_frame, width=650, height=2, bg="#bdb9b1", highlightthickness=0)
        self.menu_line.place(x=0, y=598)

        #try:
        #    expire_in = expire-datetime.today()
        #except:
        #    expire_in = expire
        expire_in = 'no expires'
        #self.expire_label = Label(self.lgn_frame, text=f'Your license will expire in: {expire_in}',bg="#040405" , fg="#dc143c",
        #                        font=("yu gothic ui", 12, "bold"))
        #self.expire_label.place(x=250, y=550)

        def open_paypal(e):
            webbrowser.open('https://paypal.me/petruleon?country.x=IT&locale.x=it_IT')
        self.donate_button = PIL.Image.open('images\\paypal.png')
        donate_photo = ImageTk.PhotoImage(self.donate_button.resize((100,50)))
        self.donate_button_label = Label(self.lgn_frame, image=donate_photo, bg='#040405')
        self.donate_button_label.image = donate_photo
        self.donate_button_label.place(x=500, y=400)

        self.donate_button_label.bind("<Button-1>",open_paypal)

        self.donate = Button(self.donate_button_label, font=("yu gothic ui", 11, "bold"), width=4, bd=0,
                            bg='#bdb9b1', cursor='hand2', activebackground='#bdb9b1', fg='black', command = lambda: open_paypal(1))
        self.donate.place(x=55, y=300)

        ### MENU BUTTONS
        self.kd_full_datas_button_menu = Button(self.lgn_frame, text = "Kingdom Datas",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command = ROKSCAN_event)
        self.kd_full_datas_button_menu.place(x = 0, y = 50)

        self.seed_scan_datas_button_menu = Button(self.lgn_frame, text = "Seed Scan",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command= seed_scan_event)
        self.seed_scan_datas_button_menu.place(x = 151, y = 50)

        self.ally_scan_button_menu = Button(self.lgn_frame, text = "Ally Scan",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black", command = ally_scan_event)
        self.ally_scan_button_menu.place(x = 302, y = 50)

        self.kvk_datas_button_menu = Button(self.lgn_frame, text = "KVK datas",font=('yu gothic ui', 10, "bold"),width=18, bd=0,
                            bg='#040405', cursor='hand2', activebackground='#bdb9b1', fg='white',disabledforeground="black")
        self.kvk_datas_button_menu.config(state="disabled", bg='#bdb9b1', fg='black')
        self.kvk_datas_button_menu.place(x = 454, y = 50)


        ##contact
        self.cnt_button = PIL.Image.open('images\\contact.png')
        cnt_photo = ImageTk.PhotoImage(self.cnt_button.resize((230,60)))
        self.cnt_button_label = Label(self.lgn_frame, image=cnt_photo, bg='#040405')
        self.cnt_button_label.image = cnt_photo
        self.cnt_button_label.place(x=400, y=475)

        self.contact = Button(self.cnt_button_label, text='CONTACT', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#7f7f7f', cursor='hand2', activebackground='#7f7f7f', fg='#bdb9b1', command= contact_event)
        self.contact.place(x=55, y=12)

        ##OPEN BUTTON

        self.merge_button = PIL.Image.open('images\\merge_button.png')
        merge_photo = ImageTk.PhotoImage(self.merge_button.resize((230,60)))
        self.merge_button_label = Label(self.lgn_frame, image=merge_photo, bg='#040405')
        self.merge_button_label.image = merge_photo
        self.merge_button_label.place(x=400, y=300)

        self.merge= Button(self.merge_button_label, text='ELABORATE', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#568E94', cursor='hand2', activebackground='#568E94', fg='black', command = open)
        self.merge.place(x=55, y=12)

        ##Entry path1

        self.path_button = PIL.Image.open('images\\entry_path.png')
        path_photo = ImageTk.PhotoImage(self.path_button.resize((230,40)))
        self.path_button_label = Label(self.lgn_frame, image=path_photo, bg='#040405')
        self.path_button_label.image = path_photo
        self.path_button_label.place(x=400, y=200)

        self.path= Button(self.path_button_label, text='open the 1st excel for kvk', font=("yu gothic ui", 10, "bold"), width=22, bd=0,
                            bg='#63A4AB', cursor='hand2', activebackground='#63A4AB', fg='black', command = path1)
        self.path.place(x=20, y=7)

        ##Entry path2

        self.path_button = PIL.Image.open('images\\entry_path.png')
        path_photo = ImageTk.PhotoImage(self.path_button.resize((230,40)))
        self.path_button_label = Label(self.lgn_frame, image=path_photo, bg='#040405')
        self.path_button_label.image = path_photo
        self.path_button_label.place(x=400, y=250)

        self.path= Button(self.path_button_label, text='open the 2nd excel for kvk', font=("yu gothic ui", 10, "bold"), width=22, bd=0,
                            bg='#63A4AB', cursor='hand2', activebackground='#63A4AB', fg='black', command = path2)
        self.path.place(x=20, y=7)

        #canvas

        #OUTPUT_PATH = Path(__file__).parent
        #ASSETS_PATH = OUTPUT_PATH / Path(r"assets\frame0")

        relative_to_assets = r"assets\\frame0\\"
        self.canvas = Canvas(
            self.lgn_frame,
            bg="#040405",
            height=501,
            width=372,
            bd=0,
            highlightthickness=0,
            relief="ridge"
        )

        self.canvas.place(x=2, y=85)

        self.canvas.create_text(
            106.0,
            154.0,
            anchor="nw",
            text="T3 Points",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            202.0,
            anchor="nw",
            text="T4 Points",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            250.0,
            anchor="nw",
            text="T5 Points",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            298.0,
            anchor="nw",
            text="deads",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            346.0,
            anchor="nw",
            text="rss assistance",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            394.0,
            anchor="nw",
            text="honor points",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            442.0,
            anchor="nw",
            text="ally helps",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.entry_image_1 = PhotoImage(
            file=relative_to_assets + "entry_1.png")
        self.entry_bg_1 = self.canvas.create_image(
            61.5,
            63.5,
            image=self.entry_image_1
        )
        self.entry_1 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_1.place(
            x=49.0,
            y=47.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_2 = PhotoImage(
            file=relative_to_assets+"entry_2.png")
        self.entry_bg_2 = self.canvas.create_image(
            61.5,
            111.5,
            image=self.entry_image_2
        )
        self.entry_2 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_2.place(
            x=49.0,
            y=95.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_3 = PhotoImage(
            file=relative_to_assets+"entry_3.png")
        self.entry_bg_3 = self.canvas.create_image(
            61.5,
            159.5,
            image=self.entry_image_3
        )
        self.entry_3 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_3.place(
            x=49.0,
            y=143.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_4 = PhotoImage(
            file=relative_to_assets+"entry_4.png")
        self.entry_bg_4 = self.canvas.create_image(
            61.5,
            207.5,
            image=self.entry_image_4
        )
        self.entry_4 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_4.place(
            x=49.0,
            y=191.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_5 = PhotoImage(
            file=relative_to_assets+"entry_5.png")
        self.entry_bg_5 = self.canvas.create_image(
            61.5,
            255.5,
            image=self.entry_image_5
        )
        self.entry_5 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_5.place(
            x=49.0,
            y=239.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_6 = PhotoImage(
            file=relative_to_assets+"entry_6.png")
        self.entry_bg_6 = self.canvas.create_image(
            61.5,
            303.5,
            image=self.entry_image_6
        )
        self.entry_6 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_6.place(
            x=49.0,
            y=287.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_7 = PhotoImage(
            file=relative_to_assets+"entry_7.png")
        self.entry_bg_7 = self.canvas.create_image(
            61.5,
            351.5,
            image=self.entry_image_7
        )
        self.entry_7 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_7.place(
            x=49.0,
            y=335.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_8 = PhotoImage(
            file=relative_to_assets+"entry_8.png")
        self.entry_bg_8 = self.canvas.create_image(
            61.5,
            399.5,
            image=self.entry_image_8
        )
        self.entry_8 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_8.place(
            x=49.0,
            y=383.0,
            width=25.0,
            height=31.0
        )

        self.entry_image_9 = PhotoImage(
            file=relative_to_assets+"entry_9.png")
        self.entry_bg_9 = self.canvas.create_image(
            61.5,
            447.5,
            image=self.entry_image_9
        )
        self.entry_9 = Entry(
            self.canvas,
            bd=0,
            bg="#999999",
            fg="#000716",
            highlightthickness=0
        )
        self.entry_9.place(
            x=49.0,
            y=431.0,
            width=25.0,
            height=31.0
        )

        self.canvas.create_text(
            16.0,
            17.0,
            anchor="nw",
            text="INSERT THE POINTS FOR EACH STAT",
            fill="#E0E0E0",
            font=("Inter", 18 * -1)
        )

        self.canvas.create_text(
            106.0,
            58.0,
            anchor="nw",
            text="T1 Points",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_text(
            106.0,
            106.0,
            anchor="nw",
            text="T2 Points",
            fill="#FFFFFF",
            font=("Inter", 15 * -1)
        )

        self.canvas.create_rectangle(
            248.5,
            61.0,
            248.5,
            458.0,
            fill="#999999",
            outline="",
            width= 2
        )











